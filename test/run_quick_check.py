#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试检查器
用于验证测试环境和运行核心测试
"""

import os
import sys
import subprocess
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

def check_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"  ✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的环境变量
    required_env_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'FLASK_SECRET_KEY']
    missing_vars = []
    
    for var in required_env_vars:
        if os.getenv(var):
            print(f"  ✓ {var}: 已设置")
        else:
            missing_vars.append(var)
            print(f"  ❌ {var}: 未设置")
    
    if missing_vars:
        print(f"\n⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必要的环境变量")
        return False
    
    # 检查必要的包
    required_packages = ['flask', 'supabase']
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✓ {package}: 已安装")
        except ImportError:
            print(f"  ❌ {package}: 未安装")
            return False

    # 检查可选包
    optional_packages = ['bs4']  # beautifulsoup4 导入为 bs4
    for package in optional_packages:
        try:
            __import__(package)
            print(f"  ✓ {package} (beautifulsoup4): 已安装")
        except ImportError:
            print(f"  ⚠️  {package} (beautifulsoup4): 未安装，部分测试可能失败")
    
    print("✅ 环境检查通过")
    return True

def run_single_test(test_path, description):
    """运行单个测试"""
    print(f"\n{'='*50}")
    print(f"🧪 {description}")
    print(f"{'='*50}")
    
    if not os.path.exists(test_path):
        print(f"❌ 测试文件不存在: {test_path}")
        return False, 0
    
    try:
        start_time = time.time()
        
        result = subprocess.run([
            sys.executable, test_path
        ], 
        cwd=project_root,
        capture_output=True, 
        text=True,
        timeout=120  # 2分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 通过 ({duration:.2f}s)")
            return True, duration
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode}, {duration:.2f}s)")
            return False, duration
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时 (>120s)")
        return False, 120
    except Exception as e:
        print(f"💥 {description} - 运行出错: {e}")
        return False, 0

def main():
    """主函数"""
    print("🧪 Web3项目深度分析报告 - 快速测试检查")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请修复环境问题后重试")
        return False
    
    # 2. 运行核心测试
    core_tests = [
        ("test/unit/test_models.py", "数据库模型单元测试"),
        ("test/security/test_csrf_protection.py", "CSRF安全测试"),
        ("test/e2e/test_homepage_template.py", "首页模板测试"),
        ("test/e2e/test_seo_features.py", "SEO功能测试")
    ]
    
    passed = 0
    total = len(core_tests)
    total_duration = 0
    
    for test_path, description in core_tests:
        success, duration = run_single_test(test_path, description)
        total_duration += duration
        if success:
            passed += 1
    
    # 3. 生成报告
    print("\n" + "=" * 60)
    print("📊 快速测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    print(f"总耗时: {total_duration:.2f}s")
    
    if passed == total:
        print("\n🎉 所有核心测试通过！")
        print("✅ 系统状态良好，可以继续开发或部署")
    elif passed >= total * 0.75:
        print(f"\n⚠️  大部分测试通过，但有 {total - passed} 个测试失败")
        print("🔧 建议检查失败的测试并修复问题")
    else:
        print(f"\n🚨 测试失败率较高，有 {total - passed} 个测试失败")
        print("🛠️  强烈建议修复所有失败测试后再继续")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed >= total * 0.75

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
