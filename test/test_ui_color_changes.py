#!/usr/bin/env python3
"""
测试UI颜色更改
验证导航栏和页脚的新样式是否正确应用
"""

import pytest
from bs4 import BeautifulSoup
import re


def test_navbar_custom_class(client):
    """测试导航栏是否使用了新的自定义样式类"""
    response = client.get('/')
    assert response.status_code == 200
    
    soup = BeautifulSoup(response.data, 'html.parser')
    navbar = soup.find('nav', class_='navbar')
    
    # 检查是否包含新的自定义类
    assert 'navbar-custom' in navbar.get('class', [])
    # 检查是否移除了旧的bg-primary类
    assert 'bg-primary' not in navbar.get('class', [])


def test_footer_custom_class(client):
    """测试页脚是否使用了新的自定义样式类"""
    response = client.get('/')
    assert response.status_code == 200
    
    soup = BeautifulSoup(response.data, 'html.parser')
    footer = soup.find('footer')
    
    # 检查是否包含新的自定义类
    assert 'footer-custom' in footer.get('class', [])
    # 检查是否移除了旧的bg-dark类
    assert 'bg-dark' not in footer.get('class', [])


def test_seo_links_hidden(client):
    """测试SEO链接是否被隐藏但仍存在于DOM中"""
    response = client.get('/')
    assert response.status_code == 200
    
    soup = BeautifulSoup(response.data, 'html.parser')
    
    # 检查SEO链接容器是否存在且有隐藏类
    seo_container = soup.find('div', class_='seo-links')
    assert seo_container is not None
    
    # 检查robots.txt链接是否存在
    robots_link = seo_container.find('a', href=lambda x: x and 'robots' in x)
    assert robots_link is not None
    assert 'Robots.txt' in robots_link.text
    
    # 检查站点地图链接是否存在
    sitemap_link = seo_container.find('a', href=lambda x: x and 'sitemap' in x)
    assert sitemap_link is not None
    assert '站点地图' in sitemap_link.text


def test_css_custom_styles_exist(client):
    """测试自定义CSS样式是否存在"""
    response = client.get('/static/css/style.css')
    assert response.status_code == 200
    
    css_content = response.data.decode('utf-8')
    
    # 检查导航栏自定义样式
    assert '.navbar-custom' in css_content
    assert 'rgba(255, 255, 255, 0.95)' in css_content
    assert 'backdrop-filter: blur(10px)' in css_content
    
    # 检查页脚自定义样式
    assert '.footer-custom' in css_content
    assert 'rgba(248, 249, 250, 0.95)' in css_content
    
    # 检查SEO链接隐藏样式
    assert '.seo-links' in css_content
    assert 'position: absolute' in css_content
    assert 'left: -9999px' in css_content


def test_color_coordination(client):
    """测试颜色协调性 - 验证使用了柔和的颜色"""
    response = client.get('/static/css/style.css')
    assert response.status_code == 200
    
    css_content = response.data.decode('utf-8')
    
    # 检查是否使用了柔和的灰色调
    assert '#495057' in css_content  # 深灰色
    assert '#6c757d' in css_content  # 中灰色
    
    # 检查是否使用了透明度
    assert 'rgba(' in css_content
    assert '0.95' in css_content  # 95%透明度


def test_responsive_navbar_styles(client):
    """测试响应式导航栏样式"""
    response = client.get('/static/css/style.css')
    assert response.status_code == 200
    
    css_content = response.data.decode('utf-8')
    
    # 检查移动端导航栏样式
    assert '.navbar-custom .navbar-collapse' in css_content
    assert 'rgba(255, 255, 255, 0.98)' in css_content
    assert 'backdrop-filter: blur(15px)' in css_content


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
