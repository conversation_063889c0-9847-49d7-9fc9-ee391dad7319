#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSRF保护安全测试
测试跨站请求伪造保护机制
"""

import os
import sys
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = True  # 启用CSRF保护
    return app

def test_csrf_token_generation():
    """测试CSRF令牌生成"""
    print("🔐 测试CSRF令牌生成...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试登录页面CSRF令牌
        response = client.get('/admin/login')
        assert response.status_code == 200, "登录页面访问失败"
        
        html_content = response.get_data(as_text=True)
        assert 'csrf_token' in html_content or 'csrf-token' in html_content, "CSRF令牌缺失"
        print("  ✓ 登录页面CSRF令牌生成正常")
        
        # 2. 测试申请页面CSRF令牌
        response = client.get('/submit_request')
        assert response.status_code == 200, "申请页面访问失败"
        
        html_content = response.get_data(as_text=True)
        assert 'csrf_token' in html_content or 'csrf-token' in html_content, "申请页面CSRF令牌缺失"
        print("  ✓ 申请页面CSRF令牌生成正常")

def test_csrf_protection_on_forms():
    """测试表单CSRF保护"""
    print("🛡️ 测试表单CSRF保护...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试无CSRF令牌的登录请求
        response = client.post('/admin/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        })
        
        # 应该被CSRF保护拒绝
        assert response.status_code == 400, "无CSRF令牌的请求应该被拒绝"
        print("  ✓ 登录表单CSRF保护正常")
        
        # 2. 测试无CSRF令牌的申请请求
        response = client.post('/submit_request', data={
            'project_name': 'Test Project',
            'official_website': 'https://test.com',
            'user_email': '<EMAIL>',
            'description': 'Test description'
        })
        
        assert response.status_code == 400, "无CSRF令牌的申请应该被拒绝"
        print("  ✓ 申请表单CSRF保护正常")

def test_csrf_protection_on_api_endpoints():
    """测试API端点CSRF保护"""
    print("🔒 测试API端点CSRF保护...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试报告删除API
        response = client.post('/admin/reports/test-id/delete')
        assert response.status_code in [400, 302, 401], "报告删除API应该受CSRF保护"
        print("  ✓ 报告删除API CSRF保护正常")
        
        # 2. 测试报告状态更新API
        response = client.post('/admin/reports/test-id/status',
                             json={'is_published': True})
        assert response.status_code in [400, 302, 401], "报告状态API应该受CSRF保护"
        print("  ✓ 报告状态API CSRF保护正常")
        
        # 3. 测试请求状态更新API
        response = client.post('/admin/requests/test-id/status',
                             json={'status': 'completed'})
        assert response.status_code in [400, 302, 401], "请求状态API应该受CSRF保护"
        print("  ✓ 请求状态API CSRF保护正常")

def test_csrf_exemption_for_safe_methods():
    """测试安全方法的CSRF豁免"""
    print("✅ 测试安全方法CSRF豁免...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # GET请求不需要CSRF保护
        safe_endpoints = [
            '/',
            '/health',
            '/admin/login',
            '/submit_request',
            '/robots.txt',
            '/sitemap.xml'
        ]
        
        for endpoint in safe_endpoints:
            response = client.get(endpoint)
            assert response.status_code == 200, f"安全端点 {endpoint} 访问失败"
        
        print("  ✓ 安全方法CSRF豁免正常")

def test_csrf_token_validation():
    """测试CSRF令牌验证"""
    print("🔍 测试CSRF令牌验证...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 获取有效的CSRF令牌
        response = client.get('/admin/login')
        html_content = response.get_data(as_text=True)
        
        # 简单提取CSRF令牌（实际实现可能需要更复杂的解析）
        import re
        csrf_match = re.search(r'csrf_token["\']?\s*[:=]\s*["\']([^"\']+)["\']', html_content)
        
        if csrf_match:
            csrf_token = csrf_match.group(1)
            print(f"  ✓ 成功提取CSRF令牌: {csrf_token[:10]}...")
            
            # 2. 使用有效CSRF令牌的请求
            response = client.post('/admin/login', data={
                'email': '<EMAIL>',
                'password': 'password',
                'csrf_token': csrf_token
            })
            
            # 应该通过CSRF验证（可能因为其他原因失败，但不是CSRF问题）
            assert response.status_code != 400, "有效CSRF令牌的请求不应该被CSRF保护拒绝"
            print("  ✓ 有效CSRF令牌验证通过")
        else:
            print("  ⚠️  无法提取CSRF令牌，跳过令牌验证测试")

def test_csrf_configuration():
    """测试CSRF配置"""
    print("⚙️ 测试CSRF配置...")
    
    app = create_test_app()
    
    # 检查CSRF配置
    assert app.config.get('WTF_CSRF_ENABLED') == True, "CSRF应该被启用"
    print("  ✓ CSRF启用配置正确")
    
    # 检查密钥配置
    assert app.config.get('SECRET_KEY'), "SECRET_KEY应该被设置"
    print("  ✓ SECRET_KEY配置正确")
    
    # 检查CSRF时间限制配置（Serverless环境应该禁用）
    if os.getenv('VERCEL'):
        assert app.config.get('WTF_CSRF_TIME_LIMIT') is None, "Serverless环境应该禁用CSRF时间限制"
        print("  ✓ Serverless环境CSRF时间限制配置正确")
    else:
        print("  ✓ 本地环境CSRF配置正确")

def test_csrf_error_handling():
    """测试CSRF错误处理"""
    print("🚨 测试CSRF错误处理...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试无效CSRF令牌
        response = client.post('/admin/login', data={
            'email': '<EMAIL>',
            'password': 'password',
            'csrf_token': 'invalid_token'
        })
        
        assert response.status_code == 400, "无效CSRF令牌应该返回400错误"
        print("  ✓ 无效CSRF令牌错误处理正常")
        
        # 2. 测试过期CSRF令牌（如果启用了时间限制）
        if not os.getenv('VERCEL'):
            # 本地环境可能有时间限制
            print("  ✓ 本地环境CSRF错误处理正常")
        else:
            print("  ✓ Serverless环境跳过时间限制测试")

def run_tests():
    """运行所有CSRF保护测试"""
    print("=" * 60)
    print("🛡️ CSRF保护安全测试")
    print("=" * 60)
    
    tests = [
        ("CSRF令牌生成测试", test_csrf_token_generation),
        ("表单CSRF保护测试", test_csrf_protection_on_forms),
        ("API端点CSRF保护测试", test_csrf_protection_on_api_endpoints),
        ("安全方法CSRF豁免测试", test_csrf_exemption_for_safe_methods),
        ("CSRF令牌验证测试", test_csrf_token_validation),
        ("CSRF配置测试", test_csrf_configuration),
        ("CSRF错误处理测试", test_csrf_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有CSRF保护测试通过！")
        print("✅ 应用具有良好的CSRF保护机制")
    else:
        print(f"\n⚠️  有 {total - passed} 个CSRF保护测试失败")
        print("🔧 建议检查和修复CSRF保护配置")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
