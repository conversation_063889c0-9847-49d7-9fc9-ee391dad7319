#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回归测试运行器
运行完整的回归测试套件，确保所有功能正常工作
"""

import os
import sys
import subprocess
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

def run_test_module(module_path, description):
    """运行单个测试模块"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"模块: {module_path}")
    print(f"{'='*60}")
    
    try:
        start_time = time.time()
        
        # 运行测试模块
        result = subprocess.run([
            sys.executable, 
            module_path
        ], 
        cwd=project_root,
        capture_output=True, 
        text=True
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 通过 ({duration:.2f}s)")
            return True, duration
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode}, {duration:.2f}s)")
            return False, duration
            
    except Exception as e:
        print(f"❌ {description} - 运行出错: {e}")
        return False, 0

def run_unit_tests():
    """运行单元测试"""
    print("\n" + "🔬 单元测试阶段")
    print("=" * 60)
    
    unit_tests = [
        ("test/unit/test_models.py", "数据库模型单元测试")
    ]
    
    passed = 0
    total_duration = 0
    
    for test_path, description in unit_tests:
        if os.path.exists(test_path):
            success, duration = run_test_module(test_path, description)
            total_duration += duration
            if success:
                passed += 1
        else:
            print(f"⚠️  测试文件不存在: {test_path}")
    
    return passed, len(unit_tests), total_duration

def run_integration_tests():
    """运行集成测试"""
    print("\n" + "🔗 集成测试阶段")
    print("=" * 60)
    
    integration_tests = [
        ("test/integration/test_api_endpoints.py", "API端点集成测试")
    ]
    
    passed = 0
    total_duration = 0
    
    for test_path, description in integration_tests:
        if os.path.exists(test_path):
            success, duration = run_test_module(test_path, description)
            total_duration += duration
            if success:
                passed += 1
        else:
            print(f"⚠️  测试文件不存在: {test_path}")
    
    return passed, len(integration_tests), total_duration

def run_e2e_tests():
    """运行端到端测试"""
    print("\n" + "🎯 端到端测试阶段")
    print("=" * 60)
    
    e2e_tests = [
        ("test/e2e/test_user_workflows.py", "用户工作流端到端测试"),
        ("test/e2e/test_admin_workflows.py", "管理员工作流端到端测试")
    ]
    
    passed = 0
    total_duration = 0
    
    for test_path, description in e2e_tests:
        if os.path.exists(test_path):
            success, duration = run_test_module(test_path, description)
            total_duration += duration
            if success:
                passed += 1
        else:
            print(f"⚠️  测试文件不存在: {test_path}")
    
    return passed, len(e2e_tests), total_duration

def run_legacy_tests():
    """运行遗留测试"""
    print("\n" + "🗂️ 遗留测试阶段")
    print("=" * 60)
    
    legacy_tests = [
        ("test/legacy/test_deployment.py", "部署验证测试"),
        ("test/legacy/test_final_verification.py", "最终验证测试"),
        ("test/legacy/test_supabase.py", "Supabase连接测试")
    ]
    
    passed = 0
    total_duration = 0
    
    for test_path, description in legacy_tests:
        if os.path.exists(test_path):
            success, duration = run_test_module(test_path, description)
            total_duration += duration
            if success:
                passed += 1
        else:
            print(f"⚠️  测试文件不存在: {test_path}")
    
    return passed, len(legacy_tests), total_duration

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "=" * 80)
    print("📊 回归测试报告")
    print("=" * 80)
    
    total_passed = 0
    total_tests = 0
    total_duration = 0
    
    for phase, (passed, total, duration) in results.items():
        total_passed += passed
        total_tests += total
        total_duration += duration
        
        success_rate = (passed / total * 100) if total > 0 else 0
        print(f"{phase}:")
        print(f"  通过: {passed}/{total} ({success_rate:.1f}%)")
        print(f"  耗时: {duration:.2f}s")
        print()
    
    overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"总体结果:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过: {total_passed}")
    print(f"  失败: {total_tests - total_passed}")
    print(f"  成功率: {overall_success_rate:.1f}%")
    print(f"  总耗时: {total_duration:.2f}s")
    
    # 生成建议
    print(f"\n📋 测试建议:")
    if overall_success_rate >= 95:
        print("  🎉 优秀！所有测试基本通过，系统状态良好")
        print("  ✅ 可以安全进行部署或发布")
    elif overall_success_rate >= 80:
        print("  ⚠️  大部分测试通过，但有一些问题需要关注")
        print("  🔧 建议修复失败的测试后再部署")
    else:
        print("  🚨 测试失败率较高，系统可能存在严重问题")
        print("  🛠️  强烈建议修复所有失败测试后再继续")
    
    return overall_success_rate >= 80

def main():
    """主函数"""
    print("🧪 Web3项目深度分析报告 - 完整回归测试套件")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 检查环境
    print("🔍 检查测试环境...")
    
    # 检查必要的环境变量
    required_env_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'FLASK_SECRET_KEY']
    missing_vars = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必要的环境变量")
    else:
        print("✅ 环境变量检查通过")
    
    # 运行各阶段测试
    results = {}
    
    try:
        # 单元测试
        unit_passed, unit_total, unit_duration = run_unit_tests()
        results["单元测试"] = (unit_passed, unit_total, unit_duration)
        
        # 集成测试
        integration_passed, integration_total, integration_duration = run_integration_tests()
        results["集成测试"] = (integration_passed, integration_total, integration_duration)
        
        # 端到端测试
        e2e_passed, e2e_total, e2e_duration = run_e2e_tests()
        results["端到端测试"] = (e2e_passed, e2e_total, e2e_duration)
        
        # 遗留测试
        legacy_passed, legacy_total, legacy_duration = run_legacy_tests()
        results["遗留测试"] = (legacy_passed, legacy_total, legacy_duration)
        
        # 生成报告
        success = generate_test_report(results)
        
        print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n\n💥 测试运行出现异常: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
