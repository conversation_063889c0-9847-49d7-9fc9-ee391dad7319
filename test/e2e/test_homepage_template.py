#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首页模板专项测试
针对 templates/public/index.html 的详细功能和SEO测试
"""

import os
import sys
import re
from unittest.mock import patch, MagicMock
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

def test_homepage_seo_structure():
    """测试首页SEO结构"""
    print("🔍 测试首页SEO结构...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        assert response.status_code == 200, "首页访问失败"
        
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查页面标题
        title = soup.find('title')
        assert title, "页面标题缺失"
        assert 'Web3项目深度分析报告' in title.text, "页面标题不正确"
        print("  ✓ 页面标题正确")
        
        # 检查meta描述
        meta_desc = soup.find('meta', {'name': 'description'})
        assert meta_desc, "meta描述缺失"
        assert 'Web3项目深度分析' in meta_desc.get('content', ''), "meta描述不正确"
        print("  ✓ meta描述正确")
        
        # 检查meta关键词
        meta_keywords = soup.find('meta', {'name': 'keywords'})
        assert meta_keywords, "meta关键词缺失"
        keywords_content = meta_keywords.get('content', '')
        required_keywords = ['Web3项目分析', '区块链项目报告', 'DeFi项目评估']
        for keyword in required_keywords:
            assert keyword in keywords_content, f"关键词 {keyword} 缺失"
        print("  ✓ meta关键词完整")
        
        # 检查结构化数据
        structured_data = soup.find('script', {'type': 'application/ld+json'})
        assert structured_data, "结构化数据缺失"
        assert 'WebSite' in structured_data.text, "结构化数据类型不正确"
        print("  ✓ 结构化数据存在")

def test_homepage_hero_section():
    """测试首页英雄区域"""
    print("🎯 测试首页英雄区域...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查英雄区域
        hero_section = soup.find('section', class_='hero-section')
        assert hero_section, "英雄区域缺失"
        print("  ✓ 英雄区域存在")
        
        # 检查主标题
        h1 = hero_section.find('h1')
        assert h1, "主标题缺失"
        assert 'Web3项目深度分析报告' in h1.text, "主标题内容不正确"
        print("  ✓ 主标题正确")
        
        # 检查副标题
        lead = hero_section.find('p', class_='lead')
        assert lead, "副标题缺失"
        assert '区块链项目研究' in lead.text, "副标题内容不正确"
        print("  ✓ 副标题正确")
        
        # 检查标签徽章
        badges = hero_section.find_all('span', class_='badge')
        assert len(badges) >= 3, "标签徽章数量不足"
        badge_texts = [badge.text for badge in badges]
        required_badges = ['区块链项目分析', 'DeFi项目评估', 'Web3投资研究']
        for required_badge in required_badges:
            assert any(required_badge in badge_text for badge_text in badge_texts), f"徽章 {required_badge} 缺失"
        print("  ✓ 标签徽章完整")
        
        # 检查申请按钮
        request_btn = hero_section.find('button', {'data-bs-target': '#requestModal'})
        assert request_btn, "申请按钮缺失"
        assert '申请项目分析' in request_btn.text, "申请按钮文本不正确"
        print("  ✓ 申请按钮正确")

def test_homepage_search_functionality():
    """测试首页搜索功能"""
    print("🔍 测试首页搜索功能...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查搜索区域
        search_section = soup.find('section', class_='search-section')
        assert search_section, "搜索区域缺失"
        print("  ✓ 搜索区域存在")
        
        # 检查搜索表单
        search_form = search_section.find('form', {'role': 'search'})
        assert search_form, "搜索表单缺失"
        print("  ✓ 搜索表单存在")
        
        # 检查搜索输入框
        search_input = search_form.find('input', {'type': 'search'})
        assert search_input, "搜索输入框缺失"
        assert search_input.get('name') == 'search', "搜索输入框name属性不正确"
        assert 'aria-label' in search_input.attrs, "搜索输入框缺少aria-label"
        print("  ✓ 搜索输入框正确")
        
        # 检查搜索按钮
        search_btn = search_form.find('button', {'type': 'submit'})
        assert search_btn, "搜索按钮缺失"
        assert 'aria-label' in search_btn.attrs, "搜索按钮缺少aria-label"
        print("  ✓ 搜索按钮正确")
        
        # 检查热门搜索链接
        hot_searches = search_section.find_all('a')
        hot_search_terms = ['DeFi', 'NFT', 'Layer2', 'GameFi']
        for term in hot_search_terms:
            found = any(term in link.text for link in hot_searches)
            assert found, f"热门搜索词 {term} 缺失"
        print("  ✓ 热门搜索链接完整")

def test_homepage_accessibility():
    """测试首页可访问性"""
    print("♿ 测试首页可访问性...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查main标签
        main = soup.find('main', {'role': 'main'})
        assert main, "main标签缺失"
        print("  ✓ main标签存在")
        
        # 检查语义化标签
        sections = soup.find_all('section')
        assert len(sections) >= 2, "section标签数量不足"
        print("  ✓ 语义化section标签存在")
        
        # 检查标题层次
        h1_count = len(soup.find_all('h1'))
        assert h1_count == 1, f"h1标签应该只有1个，实际有{h1_count}个"
        print("  ✓ h1标签唯一")
        
        h2_tags = soup.find_all('h2')
        assert len(h2_tags) >= 1, "h2标签缺失"
        print("  ✓ 标题层次正确")
        
        # 检查aria-label属性
        aria_elements = soup.find_all(attrs={'aria-label': True})
        assert len(aria_elements) >= 2, "aria-label属性使用不足"
        print("  ✓ aria-label属性使用正确")

def test_homepage_responsive_design():
    """测试首页响应式设计"""
    print("📱 测试首页响应式设计...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        
        # 检查viewport设置
        assert 'viewport' in html_content, "viewport设置缺失"
        print("  ✓ viewport设置存在")
        
        # 检查Bootstrap响应式类
        responsive_classes = ['col-lg-', 'col-md-', 'col-sm-', 'd-flex', 'flex-wrap']
        for cls in responsive_classes:
            assert cls in html_content, f"响应式类 {cls} 缺失"
        print("  ✓ Bootstrap响应式类存在")
        
        # 检查媒体查询
        assert '@media' in html_content, "CSS媒体查询缺失"
        print("  ✓ CSS媒体查询存在")

@patch('app.models.research_report.ResearchReport.get_published_reports')
def test_homepage_with_reports(mock_get_reports):
    """测试有报告数据时的首页"""
    print("📄 测试有报告数据时的首页...")
    
    # 模拟报告数据
    mock_reports = [
        {
            'id': 'report-1',
            'project_name': 'Uniswap V4',
            'description': 'Uniswap V4协议深度分析',
            'created_at': '2024-01-01',
            'is_published': True
        },
        {
            'id': 'report-2',
            'project_name': 'Arbitrum One',
            'description': 'Arbitrum Layer2解决方案分析',
            'created_at': '2024-01-02',
            'is_published': True
        }
    ]
    mock_get_reports.return_value = (mock_reports, 2)
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查报告卡片
        report_cards = soup.find_all('div', class_='report-card')
        if report_cards:  # 如果有报告卡片
            print("  ✓ 报告卡片存在")
            
            # 检查项目名称
            for report in mock_reports:
                assert report['project_name'] in html_content, f"项目 {report['project_name']} 未显示"
            print("  ✓ 项目名称显示正确")
        else:
            print("  ⚠️  报告卡片未找到（可能是模板结构不同）")

def test_homepage_search_with_query():
    """测试带搜索查询的首页"""
    print("🔍 测试带搜索查询的首页...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 测试搜索查询
        response = client.get('/?search=DeFi')
        assert response.status_code == 200, "搜索页面访问失败"
        
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查搜索输入框是否保持搜索值
        search_input = soup.find('input', {'name': 'search'})
        if search_input:
            assert search_input.get('value') == 'DeFi', "搜索值未保持"
            print("  ✓ 搜索值保持正确")
        
        # 检查清除按钮
        clear_btn = soup.find('a', class_='btn-outline-secondary')
        if clear_btn:
            assert '清除' in clear_btn.text, "清除按钮文本不正确"
            print("  ✓ 清除按钮存在")

def run_tests():
    """运行所有首页模板测试"""
    print("=" * 60)
    print("🏠 首页模板专项测试")
    print("=" * 60)
    
    tests = [
        ("首页SEO结构测试", test_homepage_seo_structure),
        ("首页英雄区域测试", test_homepage_hero_section),
        ("首页搜索功能测试", test_homepage_search_functionality),
        ("首页可访问性测试", test_homepage_accessibility),
        ("首页响应式设计测试", test_homepage_responsive_design),
        ("有报告数据时的首页测试", test_homepage_with_reports),
        ("带搜索查询的首页测试", test_homepage_search_with_query)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有首页模板测试通过！")
        print("✅ 首页SEO优化良好")
        print("✅ 用户界面完整")
        print("✅ 可访问性符合标准")
        print("✅ 响应式设计正确")
    else:
        print(f"\n⚠️  有 {total - passed} 个首页测试失败")
        print("🔧 建议检查模板结构和内容")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
