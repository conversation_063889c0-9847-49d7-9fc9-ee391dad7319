#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户工作流端到端测试
测试完整的用户使用场景和工作流程
"""

import os
import sys
import time
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

def test_homepage_browsing_workflow():
    """测试首页浏览工作流"""
    print("🏠 测试首页浏览工作流...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 访问首页
        response = client.get('/')
        assert response.status_code == 200, "首页访问失败"
        html_content = response.get_data(as_text=True)
        
        # 验证首页关键元素
        assert 'Web3项目深度分析报告' in html_content, "首页标题不正确"
        assert 'search' in html_content.lower(), "搜索功能缺失"
        print("  ✓ 步骤1: 首页加载成功")
        
        # 2. 测试搜索功能
        response = client.get('/?search=DeFi')
        assert response.status_code == 200, "搜索功能失败"
        print("  ✓ 步骤2: 搜索功能正常")
        
        # 3. 测试分页
        response = client.get('/?page=2')
        assert response.status_code == 200, "分页功能失败"
        print("  ✓ 步骤3: 分页功能正常")
        
        # 4. 测试项目申请按钮
        assert '申请新项目' in html_content or 'submit_request' in html_content, "项目申请功能缺失"
        print("  ✓ 步骤4: 项目申请入口存在")

@patch('app.models.research_report.ResearchReport.get_by_id')
@patch('app.models.research_report.ResearchReport.get_report_content')
def test_report_viewing_workflow(mock_get_content, mock_get_by_id):
    """测试报告查看工作流"""
    print("📄 测试报告查看工作流...")
    
    # 模拟报告数据
    mock_report = {
        'id': 'test-report-id',
        'project_name': 'Test DeFi Project',
        'official_website': 'https://test-defi.com',
        'is_published': True,
        'description': 'Test description',
        'analysis_file_path': '/path/to/analysis.html'
    }
    mock_get_by_id.return_value = mock_report
    mock_get_content.return_value = "# Test Report\n\nThis is a test report content."
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 查看报告详情页
        response = client.get('/report/test-report-id')
        assert response.status_code == 200, "报告详情页访问失败"
        html_content = response.get_data(as_text=True)
        
        assert 'Test DeFi Project' in html_content, "报告标题显示不正确"
        print("  ✓ 步骤1: 报告详情页加载成功")
        
        # 2. 查看Markdown报告
        response = client.get('/report/test-report-id/report')
        assert response.status_code == 200, "Markdown报告访问失败"
        print("  ✓ 步骤2: Markdown报告查看成功")
        
        # 3. 查看分析页面
        response = client.get('/report/test-report-id/analysis')
        assert response.status_code == 200, "分析页面访问失败"
        print("  ✓ 步骤3: 分析页面查看成功")
        
        # 4. 测试不存在的报告
        mock_get_by_id.return_value = None
        response = client.get('/report/non-existent-id')
        assert response.status_code == 302, "不存在的报告应该重定向"
        print("  ✓ 步骤4: 不存在报告正确处理")

@patch('app.models.user_request.UserRequest.create')
def test_project_request_workflow(mock_create):
    """测试项目申请工作流"""
    print("📝 测试项目申请工作流...")
    
    mock_create.return_value = {'id': 'test-request-id', 'project_name': 'Test Project'}
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 访问申请页面
        response = client.get('/submit_request')
        assert response.status_code == 200, "申请页面访问失败"
        html_content = response.get_data(as_text=True)
        
        # 验证表单元素
        assert 'project_name' in html_content, "项目名称字段缺失"
        assert 'official_website' in html_content, "官方网站字段缺失"
        assert 'user_email' in html_content, "用户邮箱字段缺失"
        print("  ✓ 步骤1: 申请页面加载成功")
        
        # 2. 提交有效申请
        response = client.post('/submit_request', data={
            'project_name': 'New DeFi Project',
            'official_website': 'https://new-defi.com',
            'user_email': '<EMAIL>',
            'description': 'This is a new DeFi project that needs analysis.'
        }, follow_redirects=False)
        
        assert response.status_code == 302, "申请提交后应该重定向"
        print("  ✓ 步骤2: 有效申请提交成功")
        
        # 3. 测试表单验证 - 空字段
        response = client.post('/submit_request', data={
            'project_name': '',
            'official_website': '',
            'user_email': '',
            'description': ''
        })
        
        assert response.status_code == 200, "空字段应该返回表单页面"
        print("  ✓ 步骤3: 空字段验证正常")
        
        # 4. 测试表单验证 - 无效URL
        response = client.post('/submit_request', data={
            'project_name': 'Test Project',
            'official_website': 'invalid-url',
            'user_email': '<EMAIL>',
            'description': 'Test description'
        })
        
        assert response.status_code == 200, "无效URL应该返回表单页面"
        print("  ✓ 步骤4: URL验证正常")
        
        # 5. 测试表单验证 - 无效邮箱
        response = client.post('/submit_request', data={
            'project_name': 'Test Project',
            'official_website': 'https://test.com',
            'user_email': 'invalid-email',
            'description': 'Test description'
        })
        
        assert response.status_code == 200, "无效邮箱应该返回表单页面"
        print("  ✓ 步骤5: 邮箱验证正常")

def test_seo_and_navigation_workflow():
    """测试SEO和导航工作流"""
    print("🔍 测试SEO和导航工作流...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试SEO友好URL
        seo_urls = [
            '/web3-projects/',
            '/category/defi/',
            '/category/nft/',
            '/category/layer2/',
            '/category/gamefi/'
        ]
        
        for url in seo_urls:
            response = client.get(url)
            assert response.status_code in [200, 302], f"SEO URL {url} 访问失败"
        print("  ✓ 步骤1: SEO友好URL正常")
        
        # 2. 测试sitemap.xml
        response = client.get('/sitemap.xml')
        assert response.status_code == 200, "sitemap.xml访问失败"
        assert 'xml' in response.content_type, "sitemap.xml内容类型不正确"
        print("  ✓ 步骤2: sitemap.xml生成正常")
        
        # 3. 测试robots.txt
        response = client.get('/robots.txt')
        assert response.status_code == 200, "robots.txt访问失败"
        print("  ✓ 步骤3: robots.txt访问正常")
        
        # 4. 测试面包屑导航
        response = client.get('/reports/')
        assert response.status_code in [200, 302], "报告列表页面访问失败"
        print("  ✓ 步骤4: 面包屑导航正常")

def test_mobile_responsive_workflow():
    """测试移动端响应式工作流"""
    print("📱 测试移动端响应式工作流...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟移动端User-Agent
        mobile_headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        # 1. 测试移动端首页
        response = client.get('/', headers=mobile_headers)
        assert response.status_code == 200, "移动端首页访问失败"
        html_content = response.get_data(as_text=True)
        
        # 检查响应式设计元素
        assert 'viewport' in html_content, "移动端viewport设置缺失"
        print("  ✓ 步骤1: 移动端首页正常")
        
        # 2. 测试移动端申请页面
        response = client.get('/submit_request', headers=mobile_headers)
        assert response.status_code == 200, "移动端申请页面访问失败"
        print("  ✓ 步骤2: 移动端申请页面正常")

def test_error_handling_workflow():
    """测试错误处理工作流"""
    print("⚠️ 测试错误处理工作流...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试404错误
        response = client.get('/non-existent-page')
        assert response.status_code == 404, "404错误处理失败"
        print("  ✓ 步骤1: 404错误处理正常")
        
        # 2. 测试不存在的报告
        response = client.get('/report/non-existent-report')
        assert response.status_code == 302, "不存在报告应该重定向"
        print("  ✓ 步骤2: 不存在报告错误处理正常")
        
        # 3. 测试无效的分析页面
        response = client.get('/report/non-existent-report/analysis')
        assert response.status_code == 302, "不存在分析页面应该重定向"
        print("  ✓ 步骤3: 不存在分析页面错误处理正常")

def run_tests():
    """运行所有用户工作流测试"""
    print("=" * 60)
    print("👤 用户工作流端到端测试")
    print("=" * 60)
    
    tests = [
        ("首页浏览工作流", test_homepage_browsing_workflow),
        ("报告查看工作流", test_report_viewing_workflow),
        ("项目申请工作流", test_project_request_workflow),
        ("SEO和导航工作流", test_seo_and_navigation_workflow),
        ("移动端响应式工作流", test_mobile_responsive_workflow),
        ("错误处理工作流", test_error_handling_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
