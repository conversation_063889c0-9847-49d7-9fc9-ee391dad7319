#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员工作流端到端测试
测试完整的管理员使用场景和工作流程
"""

import os
import sys
import json
from io import BytesIO
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

def create_mock_admin_user():
    """创建模拟管理员用户"""
    class MockUser:
        def __init__(self):
            self.id = 'test-admin-id'
            self.email = '<EMAIL>'
            self.username = 'admin'
            self.is_authenticated = True
            self.is_active = True
            self.is_anonymous = False
        
        def get_id(self):
            return self.id
        
        def check_password(self, password):
            return password == 'admin123'
    
    return MockUser()

@patch('app.models.admin_user.AdminUser.get_by_email')
def test_admin_login_workflow(mock_get_by_email):
    """测试管理员登录工作流"""
    print("🔐 测试管理员登录工作流...")
    
    mock_user = create_mock_admin_user()
    mock_get_by_email.return_value = mock_user
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 访问管理员入口
        response = client.get('/admin/')
        assert response.status_code == 302, "管理员入口应该重定向"
        assert '/admin/login' in response.location, "应该重定向到登录页面"
        print("  ✓ 步骤1: 管理员入口重定向正常")
        
        # 2. 访问登录页面
        response = client.get('/admin/login')
        assert response.status_code == 200, "登录页面访问失败"
        html_content = response.get_data(as_text=True)
        assert 'email' in html_content and 'password' in html_content, "登录表单缺失"
        print("  ✓ 步骤2: 登录页面加载成功")
        
        # 3. 测试错误登录
        response = client.post('/admin/login', data={
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        assert response.status_code == 200, "错误登录应该返回登录页面"
        print("  ✓ 步骤3: 错误登录处理正常")
        
        # 4. 测试正确登录
        with patch('flask_login.login_user') as mock_login:
            response = client.post('/admin/login', data={
                'email': '<EMAIL>',
                'password': 'admin123'
            }, follow_redirects=False)
            assert response.status_code == 302, "正确登录应该重定向"
            print("  ✓ 步骤4: 正确登录重定向正常")

@patch('flask_login.current_user')
@patch('app.models.research_report.ResearchReport.get_total_count')
@patch('app.models.user_request.UserRequest.get_pending_count')
def test_admin_dashboard_workflow(mock_pending_count, mock_total_count, mock_current_user):
    """测试管理员仪表板工作流"""
    print("📊 测试管理员仪表板工作流...")
    
    mock_current_user.is_authenticated = True
    mock_current_user.id = 'test-admin-id'
    mock_total_count.return_value = 10
    mock_pending_count.return_value = 3
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟登录状态
        with client.session_transaction() as sess:
            sess['_user_id'] = 'test-admin-id'
            sess['_fresh'] = True
        
        # 1. 访问仪表板
        with patch('app.models.research_report.ResearchReport.get_recent_reports') as mock_recent_reports, \
             patch('app.models.user_request.UserRequest.get_recent_requests') as mock_recent_requests:
            
            mock_recent_reports.return_value = []
            mock_recent_requests.return_value = []
            
            response = client.get('/admin/dashboard')
            assert response.status_code == 200, "仪表板访问失败"
            html_content = response.get_data(as_text=True)
            
            # 验证仪表板内容
            assert '仪表板' in html_content or 'dashboard' in html_content.lower(), "仪表板标题缺失"
            print("  ✓ 步骤1: 仪表板加载成功")

@patch('flask_login.current_user')
@patch('app.models.research_report.ResearchReport.get_all_reports')
def test_admin_reports_management_workflow(mock_get_reports, mock_current_user):
    """测试管理员报告管理工作流"""
    print("📄 测试管理员报告管理工作流...")
    
    mock_current_user.is_authenticated = True
    mock_current_user.id = 'test-admin-id'
    
    # 模拟报告数据
    mock_reports = [
        {
            'id': 'report-1',
            'project_name': 'Test Project 1',
            'is_published': True,
            'created_at': '2024-01-01'
        },
        {
            'id': 'report-2',
            'project_name': 'Test Project 2',
            'is_published': False,
            'created_at': '2024-01-02'
        }
    ]
    mock_get_reports.return_value = (mock_reports, 2)
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟登录状态
        with client.session_transaction() as sess:
            sess['_user_id'] = 'test-admin-id'
            sess['_fresh'] = True
        
        # 1. 访问报告管理页面
        response = client.get('/admin/reports')
        assert response.status_code == 200, "报告管理页面访问失败"
        html_content = response.get_data(as_text=True)
        assert 'Test Project 1' in html_content, "报告列表显示不正确"
        print("  ✓ 步骤1: 报告管理页面加载成功")
        
        # 2. 访问创建报告页面
        response = client.get('/admin/reports/create')
        assert response.status_code == 200, "创建报告页面访问失败"
        html_content = response.get_data(as_text=True)
        assert 'project_name' in html_content, "创建报告表单缺失"
        print("  ✓ 步骤2: 创建报告页面加载成功")
        
        # 3. 测试报告状态更新API
        with patch('app.models.research_report.ResearchReport.update_status') as mock_update:
            mock_update.return_value = True
            
            response = client.post('/admin/reports/report-1/status',
                                 data=json.dumps({'is_published': False}),
                                 headers={'Content-Type': 'application/json'})
            
            # 由于没有真实登录，可能返回401或302
            assert response.status_code in [200, 302, 401], "报告状态更新API异常"
            print("  ✓ 步骤3: 报告状态更新API正常")
        
        # 4. 测试报告删除API
        with patch('app.models.research_report.ResearchReport.delete') as mock_delete:
            mock_delete.return_value = True
            
            response = client.post('/admin/reports/report-1/delete')
            assert response.status_code in [200, 302, 401], "报告删除API异常"
            print("  ✓ 步骤4: 报告删除API正常")

@patch('flask_login.current_user')
@patch('app.models.user_request.UserRequest.get_all_requests')
def test_admin_requests_management_workflow(mock_get_requests, mock_current_user):
    """测试管理员请求管理工作流"""
    print("📝 测试管理员请求管理工作流...")
    
    mock_current_user.is_authenticated = True
    mock_current_user.id = 'test-admin-id'
    
    # 模拟请求数据
    mock_requests = [
        {
            'id': 'request-1',
            'project_name': 'User Request 1',
            'status': 'pending',
            'user_email': '<EMAIL>',
            'created_at': '2024-01-01'
        },
        {
            'id': 'request-2',
            'project_name': 'User Request 2',
            'status': 'processing',
            'user_email': '<EMAIL>',
            'created_at': '2024-01-02'
        }
    ]
    mock_get_requests.return_value = (mock_requests, 2)
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟登录状态
        with client.session_transaction() as sess:
            sess['_user_id'] = 'test-admin-id'
            sess['_fresh'] = True
        
        # 1. 访问请求管理页面
        response = client.get('/admin/requests')
        assert response.status_code == 200, "请求管理页面访问失败"
        html_content = response.get_data(as_text=True)
        assert 'User Request 1' in html_content, "请求列表显示不正确"
        print("  ✓ 步骤1: 请求管理页面加载成功")
        
        # 2. 测试请求详情API
        with patch('app.models.user_request.UserRequest.get_by_id') as mock_get_by_id:
            mock_get_by_id.return_value = mock_requests[0]
            
            response = client.get('/admin/requests/request-1')
            assert response.status_code in [200, 302, 401], "请求详情API异常"
            print("  ✓ 步骤2: 请求详情API正常")
        
        # 3. 测试请求状态更新API
        with patch('app.models.user_request.UserRequest.update_status') as mock_update:
            mock_update.return_value = True
            
            response = client.post('/admin/requests/request-1/status',
                                 data=json.dumps({'status': 'completed', 'admin_notes': 'Test notes'}),
                                 headers={'Content-Type': 'application/json'})
            
            assert response.status_code in [200, 302, 401], "请求状态更新API异常"
            print("  ✓ 步骤3: 请求状态更新API正常")
        
        # 4. 测试请求拒绝（删除）API
        with patch('app.models.user_request.UserRequest.delete') as mock_delete:
            mock_delete.return_value = True
            
            response = client.post('/admin/requests/request-1/status',
                                 data=json.dumps({'status': 'rejected'}),
                                 headers={'Content-Type': 'application/json'})
            
            assert response.status_code in [200, 302, 401], "请求拒绝API异常"
            print("  ✓ 步骤4: 请求拒绝API正常")

@patch('flask_login.current_user')
def test_admin_file_upload_workflow(mock_current_user):
    """测试管理员文件上传工作流"""
    print("📁 测试管理员文件上传工作流...")
    
    mock_current_user.is_authenticated = True
    mock_current_user.id = 'test-admin-id'
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟登录状态
        with client.session_transaction() as sess:
            sess['_user_id'] = 'test-admin-id'
            sess['_fresh'] = True
        
        # 创建测试文件
        test_md_content = b"# Test Report\nThis is a test markdown file."
        test_html_content = b"<html><body><h1>Test Analysis</h1></body></html>"
        
        # 1. 测试创建报告文件上传
        with patch('app.models.research_report.ResearchReport.create') as mock_create, \
             patch('app.utils.file_handler.save_uploaded_file') as mock_save_file:
            
            mock_create.return_value = {'id': 'new-report-id'}
            mock_save_file.side_effect = ['/path/to/report.md', '/path/to/analysis.html']
            
            response = client.post('/admin/reports/create', data={
                'project_name': 'Test Project',
                'official_website': 'https://test.com',
                'creator_name': 'Test Creator',
                'description': 'Test description',
                'report_file': (BytesIO(test_md_content), 'test.md'),
                'analysis_file': (BytesIO(test_html_content), 'test.html')
            })
            
            # 由于没有真实登录，可能重定向
            assert response.status_code in [200, 302, 401], "文件上传异常"
            print("  ✓ 步骤1: 创建报告文件上传正常")
        
        # 2. 测试完成请求文件上传
        with patch('app.models.user_request.UserRequest.get_by_id') as mock_get_request, \
             patch('app.models.research_report.ResearchReport.create') as mock_create_report, \
             patch('app.utils.file_handler.save_uploaded_file') as mock_save_file:
            
            mock_get_request.return_value = {
                'id': 'request-1',
                'project_name': 'User Request Project',
                'official_website': 'https://user-project.com'
            }
            mock_create_report.return_value = {'id': 'new-report-id'}
            mock_save_file.side_effect = ['/path/to/report.md', '/path/to/analysis.html']
            
            response = client.post('/admin/requests/request-1/complete', data={
                'creator_name': 'Admin Creator',
                'description': 'Completed request',
                'admin_notes': 'Request completed successfully',
                'report_file': (BytesIO(test_md_content), 'test.md'),
                'analysis_file': (BytesIO(test_html_content), 'test.html')
            })
            
            assert response.status_code in [200, 302, 401], "完成请求文件上传异常"
            print("  ✓ 步骤2: 完成请求文件上传正常")

def test_admin_security_workflow():
    """测试管理员安全工作流"""
    print("🔒 测试管理员安全工作流...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试未登录访问受保护页面
        protected_pages = [
            '/admin/dashboard',
            '/admin/reports',
            '/admin/reports/create',
            '/admin/requests'
        ]
        
        for page in protected_pages:
            response = client.get(page)
            assert response.status_code == 302, f"受保护页面 {page} 应该重定向"
        print("  ✓ 步骤1: 受保护页面访问控制正常")
        
        # 2. 测试API端点保护
        api_endpoints = [
            ('/admin/reports/test-id/delete', 'POST'),
            ('/admin/reports/test-id/status', 'POST'),
            ('/admin/requests/test-id/status', 'POST')
        ]
        
        for endpoint, method in api_endpoints:
            if method == 'POST':
                response = client.post(endpoint)
            else:
                response = client.get(endpoint)
            
            assert response.status_code in [302, 401], f"API端点 {endpoint} 应该受保护"
        print("  ✓ 步骤2: API端点保护正常")

def run_tests():
    """运行所有管理员工作流测试"""
    print("=" * 60)
    print("👨‍💼 管理员工作流端到端测试")
    print("=" * 60)
    
    tests = [
        ("管理员登录工作流", test_admin_login_workflow),
        ("管理员仪表板工作流", test_admin_dashboard_workflow),
        ("管理员报告管理工作流", test_admin_reports_management_workflow),
        ("管理员请求管理工作流", test_admin_requests_management_workflow),
        ("管理员文件上传工作流", test_admin_file_upload_workflow),
        ("管理员安全工作流", test_admin_security_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
