#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端UI端到端测试
测试所有前端页面和UI组件的功能
"""

import os
import sys
import re
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

def test_homepage_error_handling():
    """测试首页异常处理 - 确保模板变量完整"""
    print("🚨 测试首页异常处理...")

    app = create_test_app()

    with app.test_client() as client:
        # 模拟数据库异常
        with patch('app.models.research_report.ResearchReport.get_published_reports') as mock_reports:
            mock_reports.side_effect = Exception("Database connection failed")

            response = client.get('/')
            assert response.status_code == 200, "异常处理后页面应该正常显示"

            html_content = response.get_data(as_text=True)

            # 检查页面是否包含必要的模板变量，不应该出现 UndefinedError
            assert 'requests_total' not in html_content or 'UndefinedError' not in html_content, \
                "页面不应该包含未定义变量错误"

            # 检查错误消息是否正确显示
            assert '加载页面时出现错误' in html_content, "应该显示错误消息"

            # 检查页面基本结构仍然完整
            assert 'Web3项目深度分析报告' in html_content, "页面标题应该存在"
            assert 'search' in html_content, "搜索功能应该存在"

    print("✅ 首页异常处理测试通过")

def test_homepage_ui_elements():
    """测试首页UI元素"""
    print("🏠 测试首页UI元素...")

    app = create_test_app()

    with app.test_client() as client:
        response = client.get('/')
        assert response.status_code == 200, "首页访问失败"

        html_content = response.get_data(as_text=True)

        # 检查关键UI元素
        ui_elements = [
            'Web3项目深度分析报告',  # 页面标题
            'search',  # 搜索功能
            'hero-section',  # 英雄区域
            'project-list',  # 项目列表
            'pagination',  # 分页
            'footer',  # 页脚
            'navbar',  # 导航栏
            'btn',  # 按钮
            'card'  # 卡片组件
        ]

        missing_elements = []
        for element in ui_elements:
            if element not in html_content.lower():
                missing_elements.append(element)
        
        if missing_elements:
            print(f"  ⚠️  缺少UI元素: {missing_elements}")
        else:
            print("  ✓ 所有关键UI元素存在")
        
        # 检查响应式设计
        assert 'viewport' in html_content, "缺少响应式viewport设置"
        assert 'bootstrap' in html_content.lower() or 'css' in html_content, "缺少CSS框架"
        print("  ✓ 响应式设计元素正常")

def test_search_functionality_ui():
    """测试搜索功能UI"""
    print("🔍 测试搜索功能UI...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试搜索表单
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        
        assert 'search' in html_content.lower(), "搜索表单缺失"
        assert 'input' in html_content.lower(), "搜索输入框缺失"
        print("  ✓ 搜索表单UI正常")
        
        # 2. 测试搜索结果页面
        response = client.get('/?search=DeFi')
        assert response.status_code == 200, "搜索结果页面访问失败"
        
        html_content = response.get_data(as_text=True)
        assert 'defi' in html_content.lower() or '搜索' in html_content, "搜索结果显示异常"
        print("  ✓ 搜索结果UI正常")
        
        # 3. 测试空搜索
        response = client.get('/?search=')
        assert response.status_code == 200, "空搜索处理失败"
        print("  ✓ 空搜索UI处理正常")

def test_pagination_ui():
    """测试分页UI"""
    print("📄 测试分页UI...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试第一页
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        
        # 检查分页元素
        pagination_elements = ['page', 'next', 'prev', 'pagination']
        has_pagination = any(element in html_content.lower() for element in pagination_elements)
        
        if has_pagination:
            print("  ✓ 分页UI元素存在")
        else:
            print("  ⚠️  分页UI元素可能缺失")
        
        # 2. 测试第二页
        response = client.get('/?page=2')
        assert response.status_code == 200, "第二页访问失败"
        print("  ✓ 分页导航正常")
        
        # 3. 测试无效页码
        response = client.get('/?page=999')
        assert response.status_code == 200, "无效页码处理失败"
        print("  ✓ 无效页码UI处理正常")

def test_report_detail_ui():
    """测试报告详情页UI"""
    print("📄 测试报告详情页UI...")

    app = create_test_app()

    with app.test_client() as client:
        # 测试报告页面的异常处理
        with patch('app.models.research_report.ResearchReport.get_by_id') as mock_get_report:
            # 模拟报告不存在的情况
            mock_get_report.return_value = None

            response = client.get('/report/test-report-id/report')
            assert response.status_code == 302, "不存在的报告应该重定向"

            # 模拟报告存在但模板渲染正常
            mock_get_report.return_value = {
                'id': 'test-id',
                'project_name': 'Test Project',
                'is_published': True,
                'report_file_path': 'test.md',
                'created_at': '2024-01-01T00:00:00',  # 字符串格式的日期
                'updated_at': '2024-01-02T00:00:00'
            }

            with patch('app.models.research_report.ResearchReport.get_report_content') as mock_content:
                mock_content.return_value = "# Test Report Content"

                response = client.get('/report/test-report-id/report')
                # 应该能正常渲染，不会因为日期格式化问题而失败
                if response.status_code == 200:
                    html_content = response.get_data(as_text=True)
                    assert 'Test Project' in html_content, "应该包含项目名称"
                    assert 'UndefinedError' not in html_content, "不应该有模板错误"
                    print("  ✓ 报告页面渲染正常")
                else:
                    print("  ⚠️  报告页面访问失败")

        # 测试分析页面的异常处理
        with patch('app.models.research_report.ResearchReport.get_by_id') as mock_get_report:
            mock_get_report.return_value = {
                'id': 'test-id',
                'project_name': 'Test Project',
                'is_published': True,
                'analysis_file_path': 'test.html',
                'created_at': '2024-01-01T00:00:00',  # 字符串格式的日期
                'updated_at': '2024-01-02T00:00:00'
            }

            with patch('app.models.research_report.ResearchReport.get_analysis_content') as mock_content:
                mock_content.return_value = "<h1>Test Analysis Content</h1>"

                response = client.get('/report/test-report-id/analysis')
                # 应该能正常渲染，不会因为日期格式化问题而失败
                if response.status_code == 200:
                    html_content = response.get_data(as_text=True)
                    assert 'Test Project' in html_content, "应该包含项目名称"
                    assert 'UndefinedError' not in html_content, "不应该有模板错误"
                    print("  ✓ 分析页面渲染正常")
                else:
                    print("  ⚠️  分析页面访问失败")

def test_request_form_ui():
    """测试申请表单UI"""
    print("📝 测试申请表单UI...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/submit_request')
        assert response.status_code == 200, "申请表单页面访问失败"
        
        html_content = response.get_data(as_text=True)
        
        # 检查表单UI元素
        form_elements = [
            'project_name',  # 项目名称字段
            'official_website',  # 官方网站字段
            'user_email',  # 用户邮箱字段
            'description',  # 描述字段
            'submit',  # 提交按钮
            'form',  # 表单标签
            'input',  # 输入框
            'textarea'  # 文本区域
        ]
        
        missing_elements = []
        for element in form_elements:
            if element not in html_content.lower():
                missing_elements.append(element)
        
        if missing_elements:
            print(f"  ⚠️  表单元素缺失: {missing_elements}")
        else:
            print("  ✓ 所有表单元素存在")
        
        # 检查表单验证UI
        assert 'required' in html_content.lower() or 'validate' in html_content.lower(), "表单验证提示可能缺失"
        print("  ✓ 表单验证UI正常")

def test_admin_login_ui():
    """测试管理员登录UI"""
    print("🔐 测试管理员登录UI...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/admin/login')
        assert response.status_code == 200, "管理员登录页面访问失败"
        
        html_content = response.get_data(as_text=True)
        
        # 检查登录UI元素
        login_elements = [
            'email',  # 邮箱字段
            'password',  # 密码字段
            'login',  # 登录相关
            'submit',  # 提交按钮
            'form',  # 表单
            'admin'  # 管理员相关
        ]
        
        for element in login_elements:
            if element not in html_content.lower():
                print(f"  ⚠️  登录UI元素缺失: {element}")
            else:
                print(f"  ✓ 登录UI元素存在: {element}")
        
        # 检查安全元素
        assert 'csrf' in html_content.lower() or 'token' in html_content.lower(), "CSRF保护UI可能缺失"
        print("  ✓ 安全UI元素正常")

def test_responsive_design():
    """测试响应式设计"""
    print("📱 测试响应式设计...")
    
    app = create_test_app()
    
    # 模拟不同设备的User-Agent
    devices = {
        'desktop': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'tablet': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
    
    with app.test_client() as client:
        for device, user_agent in devices.items():
            headers = {'User-Agent': user_agent}
            
            # 测试首页
            response = client.get('/', headers=headers)
            assert response.status_code == 200, f"{device}设备首页访问失败"
            
            html_content = response.get_data(as_text=True)
            
            # 检查响应式设计元素
            responsive_elements = [
                'viewport',  # viewport设置
                'responsive',  # 响应式类
                'mobile',  # 移动端相关
                'col-',  # Bootstrap列
                'container'  # 容器
            ]
            
            has_responsive = any(element in html_content.lower() for element in responsive_elements)
            if has_responsive:
                print(f"  ✓ {device}设备响应式设计正常")
            else:
                print(f"  ⚠️  {device}设备响应式设计可能缺失")

def test_error_pages_ui():
    """测试错误页面UI"""
    print("⚠️ 测试错误页面UI...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 1. 测试404页面
        response = client.get('/non-existent-page')
        assert response.status_code == 404, "404页面状态码不正确"
        
        html_content = response.get_data(as_text=True)
        error_elements = ['404', 'not found', '未找到', 'error']
        
        has_error_ui = any(element in html_content.lower() for element in error_elements)
        if has_error_ui:
            print("  ✓ 404错误页面UI正常")
        else:
            print("  ⚠️  404错误页面UI可能缺失")
        
        # 2. 测试不存在的报告
        response = client.get('/report/non-existent-report')
        # 应该重定向或显示错误
        assert response.status_code in [302, 404], "不存在报告的错误处理不正确"
        print("  ✓ 报告错误页面处理正常")

def test_accessibility_features():
    """测试可访问性功能"""
    print("♿ 测试可访问性功能...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        
        # 检查可访问性元素
        accessibility_elements = [
            'alt=',  # 图片alt属性
            'aria-',  # ARIA属性
            'role=',  # 角色属性
            'title=',  # 标题属性
            'label',  # 标签
            'lang='  # 语言属性
        ]
        
        accessibility_score = 0
        for element in accessibility_elements:
            if element in html_content.lower():
                accessibility_score += 1
                print(f"  ✓ 可访问性元素存在: {element}")
        
        if accessibility_score >= len(accessibility_elements) // 2:
            print("  ✅ 可访问性功能良好")
        else:
            print("  ⚠️  可访问性功能需要改进")

def run_tests():
    """运行所有前端UI测试"""
    print("=" * 60)
    print("🎨 前端UI端到端测试")
    print("=" * 60)
    
    tests = [
        ("首页异常处理测试", test_homepage_error_handling),
        ("首页UI元素测试", test_homepage_ui_elements),
        ("搜索功能UI测试", test_search_functionality_ui),
        ("分页UI测试", test_pagination_ui),
        ("报告详情页UI测试", test_report_detail_ui),
        ("申请表单UI测试", test_request_form_ui),
        ("管理员登录UI测试", test_admin_login_ui),
        ("响应式设计测试", test_responsive_design),
        ("错误页面UI测试", test_error_pages_ui),
        ("可访问性功能测试", test_accessibility_features)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
