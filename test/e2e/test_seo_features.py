#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEO功能专项测试
测试Web3项目分析平台的搜索引擎优化功能
"""

import os
import sys
import json
import xml.etree.ElementTree as ET
from unittest.mock import patch, MagicMock
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

def test_robots_txt():
    """测试robots.txt文件"""
    print("🤖 测试robots.txt文件...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/robots.txt')
        assert response.status_code == 200, "robots.txt访问失败"
        
        content = response.get_data(as_text=True)
        
        # 检查基本内容
        assert 'User-agent:' in content, "robots.txt缺少User-agent"
        assert 'Sitemap:' in content, "robots.txt缺少Sitemap"
        print("  ✓ robots.txt基本结构正确")
        
        # 检查内容类型
        assert response.content_type == 'text/plain; charset=utf-8', "robots.txt内容类型不正确"
        print("  ✓ robots.txt内容类型正确")

def test_sitemap_xml():
    """测试sitemap.xml文件"""
    print("🗺️ 测试sitemap.xml文件...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/sitemap.xml')
        assert response.status_code == 200, "sitemap.xml访问失败"
        
        content = response.get_data(as_text=True)
        
        # 检查XML格式
        try:
            root = ET.fromstring(content)
            assert root.tag.endswith('urlset'), "sitemap.xml根元素不正确"
            print("  ✓ sitemap.xml格式正确")
        except ET.ParseError:
            assert False, "sitemap.xml格式无效"
        
        # 检查内容类型
        assert 'xml' in response.content_type, "sitemap.xml内容类型不正确"
        print("  ✓ sitemap.xml内容类型正确")
        
        # 检查必要的URL
        assert url_for('public.index', _external=True) in content or '/' in content, "首页URL缺失"
        print("  ✓ sitemap.xml包含必要URL")

def test_structured_data():
    """测试结构化数据"""
    print("📊 测试结构化数据...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找结构化数据
        structured_data_scripts = soup.find_all('script', {'type': 'application/ld+json'})
        assert len(structured_data_scripts) > 0, "结构化数据缺失"
        print("  ✓ 结构化数据存在")
        
        # 验证JSON格式
        for script in structured_data_scripts:
            try:
                data = json.loads(script.string)
                assert '@context' in data, "结构化数据缺少@context"
                assert '@type' in data, "结构化数据缺少@type"
                print(f"  ✓ 结构化数据格式正确: {data.get('@type')}")
            except json.JSONDecodeError:
                assert False, "结构化数据JSON格式无效"

def test_meta_tags():
    """测试meta标签"""
    print("🏷️ 测试meta标签...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查基本meta标签
        meta_tags = {
            'description': soup.find('meta', {'name': 'description'}),
            'keywords': soup.find('meta', {'name': 'keywords'}),
            'viewport': soup.find('meta', {'name': 'viewport'}),
            'charset': soup.find('meta', {'charset': True})
        }
        
        for tag_name, tag in meta_tags.items():
            assert tag, f"meta {tag_name} 标签缺失"
            print(f"  ✓ meta {tag_name} 存在")
        
        # 检查Open Graph标签
        og_tags = {
            'og:title': soup.find('meta', {'property': 'og:title'}),
            'og:description': soup.find('meta', {'property': 'og:description'}),
            'og:type': soup.find('meta', {'property': 'og:type'}),
            'og:url': soup.find('meta', {'property': 'og:url'})
        }
        
        for tag_name, tag in og_tags.items():
            if tag:
                print(f"  ✓ {tag_name} 存在")
            else:
                print(f"  ⚠️  {tag_name} 缺失")
        
        # 检查Twitter Card标签
        twitter_card = soup.find('meta', {'name': 'twitter:card'})
        if twitter_card:
            print("  ✓ Twitter Card 存在")
        else:
            print("  ⚠️  Twitter Card 缺失")

def test_seo_friendly_urls():
    """测试SEO友好的URL"""
    print("🔗 测试SEO友好的URL...")
    
    app = create_test_app()
    
    seo_urls = [
        '/web3-projects/',
        '/category/defi/',
        '/category/nft/',
        '/category/layer2/',
        '/category/gamefi/',
        '/reports/'
    ]
    
    with app.test_client() as client:
        for url in seo_urls:
            response = client.get(url)
            assert response.status_code in [200, 302], f"SEO URL {url} 访问失败: {response.status_code}"
            print(f"  ✓ SEO URL {url} 正常")

def test_canonical_urls():
    """测试canonical URL"""
    print("🎯 测试canonical URL...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找canonical链接
        canonical = soup.find('link', {'rel': 'canonical'})
        if canonical:
            href = canonical.get('href')
            assert href, "canonical URL为空"
            print(f"  ✓ canonical URL存在: {href}")
        else:
            print("  ⚠️  canonical URL缺失")

def test_page_speed_optimization():
    """测试页面速度优化"""
    print("⚡ 测试页面速度优化...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/')
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查CSS优化
        style_tags = soup.find_all('style')
        external_css = soup.find_all('link', {'rel': 'stylesheet'})
        
        if style_tags:
            print(f"  ✓ 内联CSS: {len(style_tags)} 个")
        if external_css:
            print(f"  ✓ 外部CSS: {len(external_css)} 个")
        
        # 检查JavaScript优化
        script_tags = soup.find_all('script')
        async_scripts = [s for s in script_tags if s.get('async')]
        defer_scripts = [s for s in script_tags if s.get('defer')]
        
        print(f"  ✓ 脚本标签: {len(script_tags)} 个")
        if async_scripts:
            print(f"  ✓ 异步脚本: {len(async_scripts)} 个")
        if defer_scripts:
            print(f"  ✓ 延迟脚本: {len(defer_scripts)} 个")
        
        # 检查图片优化
        img_tags = soup.find_all('img')
        lazy_images = [img for img in img_tags if img.get('loading') == 'lazy']
        
        if img_tags:
            print(f"  ✓ 图片标签: {len(img_tags)} 个")
            if lazy_images:
                print(f"  ✓ 懒加载图片: {len(lazy_images)} 个")

def test_mobile_optimization():
    """测试移动端优化"""
    print("📱 测试移动端优化...")
    
    app = create_test_app()
    
    mobile_user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    
    with app.test_client() as client:
        response = client.get('/', headers={'User-Agent': mobile_user_agent})
        assert response.status_code == 200, "移动端首页访问失败"
        
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查viewport设置
        viewport = soup.find('meta', {'name': 'viewport'})
        assert viewport, "viewport设置缺失"
        
        viewport_content = viewport.get('content', '')
        assert 'width=device-width' in viewport_content, "viewport缺少width=device-width"
        assert 'initial-scale=1' in viewport_content, "viewport缺少initial-scale=1"
        print("  ✓ viewport设置正确")
        
        # 检查响应式设计类
        responsive_indicators = ['col-', 'container', 'd-flex', 'flex-']
        for indicator in responsive_indicators:
            if indicator in html_content:
                print(f"  ✓ 响应式指示器存在: {indicator}")

@patch('app.models.research_report.ResearchReport.get_by_id')
def test_report_page_seo(mock_get_by_id):
    """测试报告页面SEO"""
    print("📄 测试报告页面SEO...")
    
    # 模拟报告数据
    mock_report = {
        'id': 'test-report-id',
        'project_name': 'Uniswap V4 深度分析',
        'description': 'Uniswap V4协议的全面技术分析和投资评估',
        'is_published': True,
        'created_at': '2024-01-01',
        'official_website': 'https://uniswap.org'
    }
    mock_get_by_id.return_value = mock_report
    
    app = create_test_app()
    
    with app.test_client() as client:
        response = client.get('/report/test-report-id')
        assert response.status_code == 200, "报告页面访问失败"
        
        html_content = response.get_data(as_text=True)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查页面标题
        title = soup.find('title')
        if title and mock_report['project_name'] in title.text:
            print("  ✓ 报告页面标题包含项目名称")
        
        # 检查meta描述
        meta_desc = soup.find('meta', {'name': 'description'})
        if meta_desc and mock_report['project_name'] in meta_desc.get('content', ''):
            print("  ✓ 报告页面meta描述包含项目信息")

def run_tests():
    """运行所有SEO功能测试"""
    print("=" * 60)
    print("🔍 SEO功能专项测试")
    print("=" * 60)
    
    tests = [
        ("robots.txt测试", test_robots_txt),
        ("sitemap.xml测试", test_sitemap_xml),
        ("结构化数据测试", test_structured_data),
        ("meta标签测试", test_meta_tags),
        ("SEO友好URL测试", test_seo_friendly_urls),
        ("canonical URL测试", test_canonical_urls),
        ("页面速度优化测试", test_page_speed_optimization),
        ("移动端优化测试", test_mobile_optimization),
        ("报告页面SEO测试", test_report_page_seo)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 SEO测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有SEO功能测试通过！")
        print("✅ 搜索引擎优化完善")
        print("✅ 技术SEO配置正确")
        print("✅ 移动端优化良好")
        print("✅ 页面结构符合SEO标准")
    else:
        print(f"\n⚠️  有 {total - passed} 个SEO测试失败")
        print("🔧 建议优化SEO配置和内容")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
