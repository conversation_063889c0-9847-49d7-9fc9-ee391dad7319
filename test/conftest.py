"""
测试配置文件
包含测试的通用配置和工具函数
"""

import os
import sys
import tempfile
from unittest.mock import patch
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

class TestConfig:
    """测试配置类"""
    
    # 测试环境变量
    TEST_ENV_VARS = {
        'FLASK_ENV': 'testing',
        'FLASK_SECRET_KEY': 'test-secret-key-for-testing',
        'SUPABASE_URL': os.getenv('SUPABASE_URL', 'https://test.supabase.co'),
        'SUPABASE_KEY': os.getenv('SUPABASE_KEY', 'test-key'),
        'SUPABASE_SERVICE_KEY': os.getenv('SUPABASE_SERVICE_KEY', 'test-service-key'),
        'DEBUG': 'False',
        'TESTING': 'True'
    }
    
    # Vercel环境变量
    VERCEL_ENV_VARS = {
        'VERCEL': '1',
        'USE_SUPABASE_STORAGE': 'true',
        **TEST_ENV_VARS
    }

def create_test_app():
    """创建测试应用实例"""
    with patch.dict(os.environ, TestConfig.TEST_ENV_VARS):
        from app import create_app
        app = create_app()
        app.config['TESTING'] = True
        return app

def create_vercel_test_app():
    """创建Vercel环境测试应用实例"""
    with patch.dict(os.environ, TestConfig.VERCEL_ENV_VARS):
        from app import create_app
        app = create_app()
        app.config['TESTING'] = True
        return app

def get_project_root():
    """获取项目根目录"""
    return project_root

def check_required_files():
    """检查必需的文件是否存在"""
    required_files = [
        'app/__init__.py',
        'requirements.txt',
        'vercel.json',
        'api/run.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(project_root, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    return missing_files

def check_template_files():
    """检查模板文件是否存在"""
    template_files = [
        'templates/base.html',
        'templates/admin/base.html',
        'templates/admin/login.html',
        'templates/admin/dashboard.html',
        'templates/admin/reports.html',
        'templates/admin/create_report.html',
        'templates/admin/requests.html',
        'templates/public/index.html',
        'templates/public/report.html',
        'templates/public/analysis.html'
    ]
    
    missing_templates = []
    for template_path in template_files:
        full_path = os.path.join(project_root, template_path)
        if not os.path.exists(full_path):
            missing_templates.append(template_path)
    
    return missing_templates

def print_test_header(test_name):
    """打印测试头部"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"{'='*60}")

def print_test_result(test_name, passed, message=""):
    """打印测试结果"""
    status = "✅ 通过" if passed else "❌ 失败"
    print(f"{status}: {test_name}")
    if message:
        print(f"  {message}")

class MockDatabase:
    """模拟数据库类"""
    
    def __init__(self):
        self.data = {
            'admin_users': [],
            'research_reports': [],
            'user_requests': []
        }
    
    def insert(self, table, data):
        """插入数据"""
        if table not in self.data:
            self.data[table] = []
        
        # 添加ID
        data['id'] = len(self.data[table]) + 1
        self.data[table].append(data)
        return data
    
    def select(self, table, filters=None):
        """查询数据"""
        if table not in self.data:
            return []
        
        results = self.data[table]
        
        if filters:
            filtered_results = []
            for item in results:
                match = True
                for key, value in filters.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    filtered_results.append(item)
            results = filtered_results
        
        return results
    
    def update(self, table, item_id, data):
        """更新数据"""
        if table not in self.data:
            return False
        
        for i, item in enumerate(self.data[table]):
            if item.get('id') == item_id:
                self.data[table][i].update(data)
                return True
        
        return False
    
    def delete(self, table, item_id):
        """删除数据"""
        if table not in self.data:
            return False
        
        for i, item in enumerate(self.data[table]):
            if item.get('id') == item_id:
                del self.data[table][i]
                return True
        
        return False
