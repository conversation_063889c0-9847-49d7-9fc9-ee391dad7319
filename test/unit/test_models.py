#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型单元测试
测试所有数据库模型的方法和属性
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

class TestAdminUserModel(unittest.TestCase):
    """管理员用户模型测试"""
    
    def setUp(self):
        """测试前设置"""
        from app.models.admin_user import AdminUser
        self.AdminUser = AdminUser
    
    @patch('app.services.database.db_service.execute_query')
    def test_get_by_id(self, mock_execute):
        """测试根据ID获取管理员用户"""
        # 模拟数据库返回
        mock_result = MagicMock()
        mock_result.data = [{'id': '123', 'email': '<EMAIL>', 'name': 'admin'}]
        mock_execute.return_value = mock_result

        user = self.AdminUser.get_by_id('123')

        self.assertIsNotNone(user)
        self.assertEqual(user.id, '123')
        self.assertEqual(user.email, '<EMAIL>')
        mock_execute.assert_called_once()

    @patch('app.services.database.db_service.execute_query')
    def test_get_by_email(self, mock_execute):
        """测试根据邮箱获取管理员用户"""
        mock_result = MagicMock()
        mock_result.data = [{'id': '123', 'email': '<EMAIL>', 'name': 'admin'}]
        mock_execute.return_value = mock_result

        user = self.AdminUser.get_by_email('<EMAIL>')

        self.assertIsNotNone(user)
        self.assertEqual(user.email, '<EMAIL>')
        mock_execute.assert_called_once()

    @patch('app.services.database.db_service.execute_query')
    def test_create_admin_user(self, mock_execute):
        """测试创建管理员用户"""
        mock_result = MagicMock()
        mock_result.data = [{'id': '123', 'email': '<EMAIL>', 'name': 'newadmin'}]
        mock_execute.return_value = mock_result

        user_data = {
            'email': '<EMAIL>',
            'name': 'newadmin',
            'password_hash': 'hashed_password'
        }

        user = self.AdminUser.create(user_data)

        self.assertIsNotNone(user)
        self.assertEqual(user.email, '<EMAIL>')
        mock_execute.assert_called_once()
    
    def test_check_password(self):
        """测试密码验证"""
        # 创建一个模拟用户对象
        user_data = {
            'id': '123',
            'email': '<EMAIL>',
            'password_hash': '$2b$12$test_hash'
        }
        
        user = self.AdminUser(user_data)
        
        # 由于实际的密码哈希验证需要真实的哈希，这里只测试方法存在
        self.assertTrue(hasattr(user, 'check_password'))
        self.assertTrue(callable(getattr(user, 'check_password')))


class TestResearchReportModel(unittest.TestCase):
    """研究报告模型测试"""
    
    def setUp(self):
        """测试前设置"""
        from app.models.research_report import ResearchReport
        self.ResearchReport = ResearchReport
    
    @patch('app.services.database.db_service.execute_query')
    def test_get_by_id(self, mock_execute):
        """测试根据ID获取报告"""
        mock_result = MagicMock()
        mock_result.data = [{
            'id': '123',
            'project_name': 'Test Project',
            'is_published': True,
            'created_at': datetime.now()
        }]
        mock_execute.return_value = mock_result
        
        report = self.ResearchReport.get_by_id('123')
        
        self.assertIsNotNone(report)
        self.assertEqual(report['id'], '123')
        self.assertEqual(report['project_name'], 'Test Project')
        mock_execute.assert_called_once()
    
    @patch('app.services.database.db_service.execute_query')
    def test_get_published_reports(self, mock_execute):
        """测试获取已发布报告"""
        mock_result = MagicMock()
        mock_result.data = [
            {'id': '1', 'project_name': 'Project 1', 'is_published': True, 'created_at': '2024-01-01'},
            {'id': '2', 'project_name': 'Project 2', 'is_published': True, 'created_at': '2024-01-02'}
        ]
        mock_result.count = 2
        mock_execute.return_value = mock_result

        reports, total = self.ResearchReport.get_published_reports(page=1, per_page=10)

        self.assertEqual(len(reports), 2)
        self.assertEqual(total, 2)
        self.assertTrue(all(report['is_published'] for report in reports))
        # 由于get_published_reports会调用两次execute_query（一次计数，一次获取数据）
        self.assertEqual(mock_execute.call_count, 2)
    
    @patch('app.services.database.db_service.execute_query')
    def test_create_report(self, mock_execute):
        """测试创建报告"""
        mock_result = MagicMock()
        mock_result.data = [{
            'id': '123',
            'project_name': 'New Project',
            'official_website': 'https://example.com',
            'creator_name': 'Test Creator'
        }]
        mock_execute.return_value = mock_result
        
        report_data = {
            'project_name': 'New Project',
            'official_website': 'https://example.com',
            'creator_name': 'Test Creator',
            'report_file_path': '/path/to/report.md',
            'analysis_file_path': '/path/to/analysis.html'
        }
        
        report = self.ResearchReport.create(report_data)
        
        self.assertIsNotNone(report)
        self.assertEqual(report['project_name'], 'New Project')
        mock_execute.assert_called_once()
    
    @patch('app.services.database.db_service.execute_query')
    def test_update_status(self, mock_execute):
        """测试更新报告状态"""
        mock_result = MagicMock()
        mock_result.data = [{'id': '123', 'is_published': False}]
        mock_execute.return_value = mock_result
        
        success = self.ResearchReport.update_status('123', False)
        
        self.assertTrue(success)
        mock_execute.assert_called_once()
    
    @patch('app.services.database.db_service.execute_query')
    def test_delete_report(self, mock_execute):
        """测试删除报告"""
        mock_result = MagicMock()
        mock_result.data = []
        mock_execute.return_value = mock_result
        
        success = self.ResearchReport.delete('123')
        
        self.assertTrue(success)
        mock_execute.assert_called_once()


class TestUserRequestModel(unittest.TestCase):
    """用户请求模型测试"""
    
    def setUp(self):
        """测试前设置"""
        from app.models.user_request import UserRequest
        self.UserRequest = UserRequest
    
    @patch('app.services.database.db_service.execute_query')
    def test_get_by_id(self, mock_execute):
        """测试根据ID获取用户请求"""
        mock_result = MagicMock()
        mock_result.data = [{
            'id': '123',
            'project_name': 'Test Request',
            'status': 'pending',
            'created_at': datetime.now()
        }]
        mock_execute.return_value = mock_result
        
        request = self.UserRequest.get_by_id('123')
        
        self.assertIsNotNone(request)
        self.assertEqual(request['id'], '123')
        self.assertEqual(request['project_name'], 'Test Request')
        mock_execute.assert_called_once()
    
    @patch('app.services.database.db_service.execute_query')
    def test_create_request(self, mock_execute):
        """测试创建用户请求"""
        mock_result = MagicMock()
        mock_result.data = [{
            'id': '123',
            'project_name': 'New Request',
            'official_website': 'https://example.com',
            'user_email': '<EMAIL>'
        }]
        mock_execute.return_value = mock_result
        
        request_data = {
            'project_name': 'New Request',
            'official_website': 'https://example.com',
            'user_email': '<EMAIL>',
            'description': 'Test description'
        }
        
        request = self.UserRequest.create(request_data)
        
        self.assertIsNotNone(request)
        self.assertEqual(request['project_name'], 'New Request')
        mock_execute.assert_called_once()
    
    @patch('app.services.database.db_service.execute_query')
    def test_update_status(self, mock_execute):
        """测试更新请求状态"""
        mock_result = MagicMock()
        mock_result.data = [{'id': '123', 'status': 'completed'}]
        mock_execute.return_value = mock_result
        
        success = self.UserRequest.update_status('123', 'completed', 'admin_id', 'Test notes')
        
        self.assertTrue(success)
        mock_execute.assert_called_once()
    
    @patch('app.services.database.db_service.execute_query')
    def test_get_pending_count(self, mock_execute):
        """测试获取待处理请求数量"""
        mock_result = MagicMock()
        mock_result.data = [
            {'id': '1', 'status': 'pending'},
            {'id': '2', 'status': 'pending'},
            {'id': '3', 'status': 'pending'},
            {'id': '4', 'status': 'pending'},
            {'id': '5', 'status': 'pending'}
        ]
        mock_execute.return_value = mock_result

        count = self.UserRequest.get_pending_count()

        self.assertEqual(count, 5)
        mock_execute.assert_called_once()


def run_tests():
    """运行所有模型测试"""
    print("=" * 60)
    print("🧪 数据库模型单元测试")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestAdminUserModel))
    suite.addTests(loader.loadTestsFromTestCase(TestResearchReportModel))
    suite.addTests(loader.loadTestsFromTestCase(TestUserRequestModel))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
