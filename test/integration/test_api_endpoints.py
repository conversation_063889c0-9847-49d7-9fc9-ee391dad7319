#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API端点集成测试
测试所有REST API端点的功能和响应
"""

import os
import sys
import json
import tempfile
from io import BytesIO
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False  # 禁用CSRF以便测试
    return app

def test_public_endpoints():
    """测试公共端点"""
    print("🌐 测试公共端点...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 测试首页
        response = client.get('/')
        assert response.status_code == 200, f"首页访问失败: {response.status_code}"
        assert 'Web3项目深度分析报告' in response.get_data(as_text=True), "首页内容不正确"
        print("  ✓ 首页 (/) - 正常")
        
        # 测试健康检查
        response = client.get('/health')
        assert response.status_code == 200, f"健康检查失败: {response.status_code}"
        print("  ✓ 健康检查 (/health) - 正常")
        
        # 测试robots.txt
        response = client.get('/robots.txt')
        assert response.status_code == 200, f"robots.txt访问失败: {response.status_code}"
        print("  ✓ robots.txt - 正常")
        
        # 测试sitemap.xml
        response = client.get('/sitemap.xml')
        assert response.status_code == 200, f"sitemap.xml访问失败: {response.status_code}"
        assert 'xml' in response.content_type, "sitemap.xml内容类型不正确"
        print("  ✓ sitemap.xml - 正常")
        
        # 测试SEO友好的URL
        response = client.get('/web3-projects/')
        assert response.status_code in [200, 302], f"Web3项目页面访问失败: {response.status_code}"
        print("  ✓ SEO友好URL (/web3-projects/) - 正常")
        
        # 测试分类页面
        categories = ['defi', 'nft', 'layer2', 'gamefi']
        for category in categories:
            response = client.get(f'/category/{category}/')
            assert response.status_code in [200, 302], f"分类页面 {category} 访问失败: {response.status_code}"
            print(f"  ✓ 分类页面 (/category/{category}/) - 正常")

def test_admin_authentication():
    """测试管理员认证端点"""
    print("🔐 测试管理员认证...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 测试登录页面
        response = client.get('/admin/login')
        assert response.status_code == 200, f"登录页面访问失败: {response.status_code}"
        print("  ✓ 登录页面 (/admin/login) - 正常")
        
        # 测试管理员入口重定向
        response = client.get('/admin/')
        assert response.status_code == 302, f"管理员入口重定向失败: {response.status_code}"
        print("  ✓ 管理员入口 (/admin/) - 重定向正常")
        
        # 测试安全入口
        response = client.get('/admin/secure-entry')
        assert response.status_code == 302, f"安全入口重定向失败: {response.status_code}"
        print("  ✓ 安全入口 (/admin/secure-entry) - 重定向正常")
        
        # 测试未授权访问受保护页面
        protected_pages = ['/admin/dashboard', '/admin/reports', '/admin/requests']
        for page in protected_pages:
            response = client.get(page)
            assert response.status_code == 302, f"受保护页面 {page} 应该重定向: {response.status_code}"
            print(f"  ✓ 受保护页面 ({page}) - 正确重定向")

@patch('app.models.admin_user.AdminUser.get_by_email')
@patch('app.models.admin_user.AdminUser.check_password')
def test_admin_login_process(mock_check_password, mock_get_by_email):
    """测试管理员登录流程"""
    print("🔑 测试管理员登录流程...")
    
    # 模拟用户数据
    mock_user = {
        'id': 'test-admin-id',
        'email': '<EMAIL>',
        'username': 'admin'
    }
    mock_get_by_email.return_value = mock_user
    mock_check_password.return_value = True
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 测试正确的登录
        response = client.post('/admin/login', data={
            'email': '<EMAIL>',
            'password': 'correct_password'
        }, follow_redirects=False)
        
        assert response.status_code == 302, f"登录后应该重定向: {response.status_code}"
        print("  ✓ 正确登录 - 重定向正常")
        
        # 测试错误的邮箱
        mock_get_by_email.return_value = None
        response = client.post('/admin/login', data={
            'email': '<EMAIL>',
            'password': 'password'
        })
        
        assert response.status_code == 200, f"错误登录应该返回登录页面: {response.status_code}"
        print("  ✓ 错误邮箱 - 正确处理")
        
        # 测试空字段
        response = client.post('/admin/login', data={
            'email': '',
            'password': ''
        })
        
        assert response.status_code == 200, f"空字段应该返回登录页面: {response.status_code}"
        print("  ✓ 空字段验证 - 正确处理")

def test_user_request_submission():
    """测试用户请求提交"""
    print("📝 测试用户请求提交...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 测试正确的请求提交
        with patch('app.models.user_request.UserRequest.create') as mock_create:
            mock_create.return_value = {'id': 'test-request-id'}
            
            response = client.post('/submit_request', data={
                'project_name': 'Test Project',
                'official_website': 'https://test.com',
                'user_email': '<EMAIL>',
                'description': 'Test description'
            }, follow_redirects=False)
            
            assert response.status_code == 302, f"请求提交后应该重定向: {response.status_code}"
            print("  ✓ 正确请求提交 - 成功")
        
        # 测试无效URL
        response = client.post('/submit_request', data={
            'project_name': 'Test Project',
            'official_website': 'invalid-url',
            'user_email': '<EMAIL>',
            'description': 'Test description'
        })
        
        assert response.status_code == 200, f"无效URL应该返回表单页面: {response.status_code}"
        print("  ✓ 无效URL验证 - 正确处理")
        
        # 测试空字段
        response = client.post('/submit_request', data={
            'project_name': '',
            'official_website': '',
            'user_email': '',
            'description': ''
        })
        
        assert response.status_code == 200, f"空字段应该返回表单页面: {response.status_code}"
        print("  ✓ 空字段验证 - 正确处理")

@patch('app.models.research_report.ResearchReport.delete')
def test_admin_api_endpoints(mock_delete):
    """测试管理员API端点"""
    print("🔧 测试管理员API端点...")
    
    mock_delete.return_value = True
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟登录状态
        with client.session_transaction() as sess:
            sess['_user_id'] = 'test-admin-id'
            sess['_fresh'] = True
        
        # 测试删除报告API
        response = client.post('/admin/reports/test-id/delete',
                             headers={'Content-Type': 'application/json'})
        
        # 由于没有真实的登录用户，这里主要测试路由存在
        assert response.status_code in [200, 302, 401], f"删除报告API响应异常: {response.status_code}"
        print("  ✓ 删除报告API - 路由存在")
        
        # 测试更新报告状态API
        response = client.post('/admin/reports/test-id/status',
                             data=json.dumps({'is_published': True}),
                             headers={'Content-Type': 'application/json'})
        
        assert response.status_code in [200, 302, 401], f"更新报告状态API响应异常: {response.status_code}"
        print("  ✓ 更新报告状态API - 路由存在")
        
        # 测试更新请求状态API
        response = client.post('/admin/requests/test-id/status',
                             data=json.dumps({'status': 'completed'}),
                             headers={'Content-Type': 'application/json'})
        
        assert response.status_code in [200, 302, 401], f"更新请求状态API响应异常: {response.status_code}"
        print("  ✓ 更新请求状态API - 路由存在")

def test_file_upload_endpoints():
    """测试文件上传端点"""
    print("📁 测试文件上传端点...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 创建测试文件
        test_md_content = b"# Test Report\nThis is a test markdown file."
        test_html_content = b"<html><body><h1>Test Analysis</h1></body></html>"
        
        # 测试创建报告（需要登录）
        response = client.post('/admin/reports/create', data={
            'project_name': 'Test Project',
            'official_website': 'https://test.com',
            'creator_name': 'Test Creator',
            'description': 'Test description',
            'report_file': (BytesIO(test_md_content), 'test.md'),
            'analysis_file': (BytesIO(test_html_content), 'test.html')
        })
        
        # 由于没有登录，应该重定向到登录页面
        assert response.status_code == 302, f"未登录上传应该重定向: {response.status_code}"
        print("  ✓ 文件上传保护 - 正确重定向")

def run_tests():
    """运行所有API端点测试"""
    print("=" * 60)
    print("🔌 API端点集成测试")
    print("=" * 60)
    
    tests = [
        ("公共端点测试", test_public_endpoints),
        ("管理员认证测试", test_admin_authentication),
        ("管理员登录流程测试", test_admin_login_process),
        ("用户请求提交测试", test_user_request_submission),
        ("管理员API端点测试", test_admin_api_endpoints),
        ("文件上传端点测试", test_file_upload_endpoints)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    return passed == total


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
