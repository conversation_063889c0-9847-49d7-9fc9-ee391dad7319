#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板修复验证测试
验证所有模板中的日期格式化问题已修复
"""

import os
import sys
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

def create_test_app():
    """创建测试应用"""
    from app import create_app
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app

def test_index_template_fix():
    """测试首页模板修复"""
    print("🏠 测试首页模板修复...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟有报告数据的情况
        with patch('app.models.research_report.ResearchReport.get_published_reports') as mock_reports, \
             patch('app.models.user_request.UserRequest.get_public_requests') as mock_requests:
            
            # 模拟返回带有字符串日期的报告数据
            mock_reports.return_value = ([{
                'id': 'test-id',
                'project_name': 'Test Project',
                'created_at': '2024-01-01T00:00:00',  # 字符串格式
                'updated_at': '2024-01-02T00:00:00'
            }], 1)
            
            mock_requests.return_value = ([], 0)
            
            response = client.get('/')
            assert response.status_code == 200, "首页应该正常显示"
            
            html_content = response.get_data(as_text=True)
            assert 'UndefinedError' not in html_content, "不应该有模板错误"
            assert 'isoformat' not in html_content, "不应该有isoformat调用错误"
            
    print("  ✅ 首页模板修复验证通过")

def test_analysis_template_fix():
    """测试分析页面模板修复"""
    print("📊 测试分析页面模板修复...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        with patch('app.models.research_report.ResearchReport.get_by_id') as mock_get_report, \
             patch('app.models.research_report.ResearchReport.get_analysis_content') as mock_content:
            
            # 模拟返回带有字符串日期的报告数据
            mock_get_report.return_value = {
                'id': 'test-id',
                'project_name': 'Test Project',
                'is_published': True,
                'analysis_file_path': 'test.html',
                'created_at': '2024-01-01T00:00:00',  # 字符串格式
                'updated_at': '2024-01-02T00:00:00'
            }
            
            mock_content.return_value = "<h1>Test Analysis</h1>"
            
            response = client.get('/report/test-id/analysis')
            assert response.status_code == 200, "分析页面应该正常显示"
            
            html_content = response.get_data(as_text=True)
            assert 'UndefinedError' not in html_content, "不应该有模板错误"
            assert 'isoformat' not in html_content, "不应该有isoformat调用错误"
            assert 'Test Project' in html_content, "应该包含项目名称"
            
    print("  ✅ 分析页面模板修复验证通过")

def test_report_template_fix():
    """测试报告页面模板修复"""
    print("📄 测试报告页面模板修复...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        with patch('app.models.research_report.ResearchReport.get_by_id') as mock_get_report, \
             patch('app.models.research_report.ResearchReport.get_report_content') as mock_content:
            
            # 模拟返回带有字符串日期的报告数据
            mock_get_report.return_value = {
                'id': 'test-id',
                'project_name': 'Test Project',
                'is_published': True,
                'report_file_path': 'test.md',
                'created_at': '2024-01-01T00:00:00',  # 字符串格式
                'updated_at': '2024-01-02T00:00:00'
            }
            
            mock_content.return_value = "# Test Report Content"
            
            response = client.get('/report/test-id/report')
            assert response.status_code == 200, "报告页面应该正常显示"
            
            html_content = response.get_data(as_text=True)
            assert 'UndefinedError' not in html_content, "不应该有模板错误"
            assert 'isoformat' not in html_content, "不应该有isoformat调用错误"
            assert 'Test Project' in html_content, "应该包含项目名称"
            
    print("  ✅ 报告页面模板修复验证通过")

def test_exception_handling_fix():
    """测试异常处理修复"""
    print("🚨 测试异常处理修复...")
    
    app = create_test_app()
    
    with app.test_client() as client:
        # 模拟数据库异常
        with patch('app.models.research_report.ResearchReport.get_published_reports') as mock_reports:
            mock_reports.side_effect = Exception("Database error")
            
            response = client.get('/')
            assert response.status_code == 200, "异常情况下页面应该正常显示"
            
            html_content = response.get_data(as_text=True)
            assert 'requests_total' not in html_content or 'UndefinedError' not in html_content, \
                "异常处理应该提供所有必要的模板变量"
            assert '加载页面时出现错误' in html_content, "应该显示错误消息"
            
    print("  ✅ 异常处理修复验证通过")

def run_tests():
    """运行所有模板修复测试"""
    print("=" * 60)
    print("🔧 模板修复验证测试")
    print("=" * 60)
    
    tests = [
        ("首页模板修复", test_index_template_fix),
        ("分析页面模板修复", test_analysis_template_fix),
        ("报告页面模板修复", test_report_template_fix),
        ("异常处理修复", test_exception_handling_fix)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n--- {test_name} ---")
            test_func()
            print(f"✅ {test_name} - 通过")
            passed += 1
        except Exception as e:
            print(f"❌ {test_name} - 失败: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    print(f"总测试数: {passed + failed}")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {passed / (passed + failed) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有模板修复验证通过！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return False

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
