#!/usr/bin/env python3
"""
统一测试运行器
运行所有测试模块
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_test_module(module_name, description):
    """运行单个测试模块"""
    print(f"\n{'='*60}")
    print(f"运行测试: {description}")
    print(f"模块: {module_name}")
    print(f"{'='*60}")
    
    try:
        # 获取当前脚本所在目录
        test_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录
        project_root = os.path.dirname(test_dir)
        
        # 运行测试模块
        result = subprocess.run([
            sys.executable, 
            os.path.join(test_dir, module_name)
        ], 
        cwd=project_root,
        capture_output=True, 
        text=True
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - 通过")
            return True
        else:
            print(f"❌ {description} - 失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ {description} - 运行出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Web3项目深度分析报告 - 完整测试套件")
    print("=" * 80)
    
    # 定义所有测试模块
    test_modules = [
        # 新的综合测试
        ("unit/test_models.py", "数据库模型单元测试"),
        ("integration/test_api_endpoints.py", "API端点集成测试"),
        ("e2e/test_user_workflows.py", "用户工作流端到端测试"),
        ("e2e/test_admin_workflows.py", "管理员工作流端到端测试"),
        ("security/test_csrf_protection.py", "CSRF保护安全测试"),

        # 遗留测试（保持兼容性）
        ("legacy/test_deployment.py", "部署验证测试"),
        ("legacy/test_final_verification.py", "最终验证测试"),
        ("legacy/test_supabase.py", "Supabase连接测试")
    ]
    
    passed = 0
    total = len(test_modules)
    failed_tests = []
    
    for module_name, description in test_modules:
        if run_test_module(module_name, description):
            passed += 1
        else:
            failed_tests.append(description)
    
    # 输出总结
    print("\n" + "=" * 80)
    print("🏁 测试总结")
    print("=" * 80)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if failed_tests:
        print("\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"  - {test}")
    
    if passed == total:
        print("\n🎉 所有测试通过！应用已准备好部署。")
        print("\n📋 部署检查清单:")
        print("  ✅ 所有模板文件存在")
        print("  ✅ 路由配置正确")
        print("  ✅ 数据库连接正常")
        print("  ✅ 安全功能配置")
        print("  ✅ Vercel配置验证")
        print("  ✅ 管理员功能完整")
        
        print("\n🚀 部署步骤:")
        print("  1. 在Vercel中设置环境变量")
        print("  2. 推送代码到Git仓库")
        print("  3. 连接Vercel并部署")
        print("  4. 运行 python create_admin.py 创建管理员")
        
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请先修复问题再部署。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
