#!/usr/bin/env python3
"""
最终验证测试脚本 - 确保所有功能正常工作
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_all_admin_templates_render():
    """测试所有管理员模板是否能正常渲染"""
    print("测试管理员模板渲染...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            with app.test_request_context():
                from flask import render_template
                
                # 测试各个模板
                templates_to_test = [
                    ('admin/login.html', {}),
                    ('admin/dashboard.html', {
                        'stats': {
                            'total_reports': 0,
                            'pending_requests': 0,
                            'recent_reports': [],
                            'recent_requests': []
                        }
                    }),
                    ('admin/reports.html', {
                        'reports': [],
                        'pagination': {'total': 0, 'total_pages': 1, 'page': 1, 'has_prev': False, 'has_next': False}
                    }),
                    ('admin/create_report.html', {}),
                    ('admin/requests.html', {
                        'requests': [],
                        'pagination': {'total': 0, 'total_pages': 1, 'page': 1, 'has_prev': False, 'has_next': False},
                        'status_filter': 'all'
                    })
                ]
                
                for template_name, context in templates_to_test:
                    try:
                        rendered = render_template(template_name, **context)
                        if rendered and len(rendered) > 100:  # 确保模板有实际内容
                            print(f"✓ {template_name} 渲染成功")
                        else:
                            print(f"✗ {template_name} 渲染内容过少")
                            return False
                    except Exception as e:
                        print(f"✗ {template_name} 渲染失败: {e}")
                        return False
                
                print("✓ 所有管理员模板渲染正常")
                return True
                
    except Exception as e:
        print(f"✗ 模板渲染测试失败: {e}")
        return False

def test_admin_routes_protection():
    """测试管理员路由保护"""
    print("测试管理员路由保护...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            protected_routes = [
                '/admin/dashboard',
                '/admin/reports',
                '/admin/reports/create',
                '/admin/requests'
            ]
            
            for route in protected_routes:
                response = client.get(route)
                # 未登录应该重定向到登录页面或返回401
                if response.status_code in [302, 401]:
                    print(f"✓ {route} 正确保护")
                else:
                    print(f"✗ {route} 保护失效: {response.status_code}")
                    return False
            
            print("✓ 所有管理员路由正确保护")
            return True
            
    except Exception as e:
        print(f"✗ 路由保护测试失败: {e}")
        return False

def test_public_routes():
    """测试公共路由"""
    print("测试公共路由...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            public_routes = [
                '/',
                '/health',
                '/admin/login'
            ]
            
            for route in public_routes:
                response = client.get(route)
                if response.status_code == 200:
                    print(f"✓ {route} 访问正常")
                else:
                    print(f"✗ {route} 访问异常: {response.status_code}")
                    return False
            
            print("✓ 所有公共路由正常")
            return True
            
    except Exception as e:
        print(f"✗ 公共路由测试失败: {e}")
        return False

def test_database_models():
    """测试数据库模型"""
    print("测试数据库模型...")
    
    try:
        from app.models.admin_user import AdminUser
        from app.models.research_report import ResearchReport
        from app.models.user_request import UserRequest
        
        # 测试模型方法是否存在
        models_and_methods = [
            (AdminUser, ['get_by_id', 'get_by_email', 'create', 'check_password']),
            (ResearchReport, ['get_by_id', 'create', 'get_published_reports', 'get_all_reports']),
            (UserRequest, ['get_by_id', 'create', 'get_all_requests', 'update_status'])
        ]
        
        for model, methods in models_and_methods:
            for method in methods:
                if not hasattr(model, method):
                    print(f"✗ {model.__name__} 缺少方法: {method}")
                    return False
            print(f"✓ {model.__name__} 模型方法完整")
        
        print("✓ 所有数据库模型正常")
        return True
        
    except Exception as e:
        print(f"✗ 数据库模型测试失败: {e}")
        return False

def test_file_handlers():
    """测试文件处理器"""
    print("测试文件处理器...")

    try:
        from app.utils.file_handler import allowed_file

        # 测试文件类型验证
        test_cases = [
            ('test.md', ['md'], True),
            ('test.html', ['html', 'htm'], True),
            ('test.txt', ['md'], False),
            ('test.pdf', ['html'], False)
        ]

        for filename, allowed_extensions, expected in test_cases:
            result = allowed_file(filename, allowed_extensions)
            if result != expected:
                print(f"✗ 文件验证失败: {filename} -> {result}, 期望: {expected}")
                return False

        print("✓ 文件处理器正常")
        return True

    except Exception as e:
        print(f"✗ 文件处理器测试失败: {e}")
        return False

def test_security_features():
    """测试安全功能"""
    print("测试安全功能...")
    
    try:
        from app import create_app
        app = create_app()
        
        # 检查CSRF保护是否启用
        if not app.config.get('WTF_CSRF_ENABLED', False):
            print("✗ CSRF保护未启用")
            return False
        
        # 检查密钥是否设置
        if not app.config.get('SECRET_KEY') or app.config['SECRET_KEY'] == 'dev-secret-key':
            print("! 警告: 使用默认密钥，生产环境请更换")
        
        print("✓ 安全功能配置正常")
        return True
        
    except Exception as e:
        print(f"✗ 安全功能测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("测试错误处理...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # 测试404错误
            response = client.get('/nonexistent-page')
            if response.status_code == 404:
                print("✓ 404错误处理正常")
            else:
                print(f"✗ 404错误处理异常: {response.status_code}")
                return False
        
        print("✓ 错误处理正常")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def run_final_verification():
    """运行最终验证测试"""
    print("=" * 70)
    print("最终验证测试 - 确保所有功能正常工作")
    print("=" * 70)
    
    tests = [
        ("管理员模板渲染", test_all_admin_templates_render),
        ("管理员路由保护", test_admin_routes_protection),
        ("公共路由", test_public_routes),
        ("数据库模型", test_database_models),
        ("文件处理器", test_file_handlers),
        ("安全功能", test_security_features),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 70)
    print(f"最终验证测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有最终验证测试通过！")
        print("\n✅ 应用已准备好部署到Vercel")
        print("\n部署步骤:")
        print("1. 确保在Vercel中设置了环境变量:")
        print("   - SUPABASE_URL")
        print("   - SUPABASE_KEY")
        print("   - FLASK_SECRET_KEY")
        print("2. 推送代码到Git仓库")
        print("3. 在Vercel中连接仓库并部署")
        print("4. 部署后运行 python create_admin.py 创建管理员用户")
    else:
        print("❌ 部分最终验证测试失败，请先修复问题。")
    
    return passed == total

if __name__ == '__main__':
    success = run_final_verification()
    sys.exit(0 if success else 1)
