#!/usr/bin/env python3
"""
测试 CSRF 修复
验证 CSRF 令牌在 serverless 环境中正常工作
"""

import os
import sys
from unittest.mock import patch
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_csrf_configuration():
    """测试 CSRF 配置"""
    print("测试 CSRF 配置...")
    
    # 模拟 Vercel 环境
    with patch.dict(os.environ, {'VERCEL': '1', 'FLASK_SECRET_KEY': 'test-secret-key'}):
        try:
            from app import create_app
            app = create_app()
            
            with app.app_context():
                print(f"✅ SECRET_KEY 已设置: {'SECRET_KEY' in app.config}")
                print(f"✅ CSRF 已启用: {app.config.get('WTF_CSRF_ENABLED', False)}")
                print(f"✅ CSRF 时间限制: {app.config.get('WTF_CSRF_TIME_LIMIT')}")
                print(f"✅ Session Cookie Secure: {app.config.get('SESSION_COOKIE_SECURE', False)}")
                
                # 测试 CSRF 令牌生成
                from flask_wtf.csrf import generate_csrf
                token = generate_csrf()
                print(f"✅ CSRF 令牌生成成功: {len(token) > 0}")
                
            return True
            
        except Exception as e:
            print(f"❌ CSRF 配置测试失败: {e}")
            return False

def test_login_form_csrf():
    """测试登录表单 CSRF 令牌"""
    print("\n测试登录表单 CSRF 令牌...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # 获取登录页面
            response = client.get('/admin/login')
            
            if response.status_code == 200:
                content = response.get_data(as_text=True)
                
                # 检查是否包含 CSRF 令牌
                has_csrf_token = 'csrf_token()' in content or 'csrf-token' in content
                print(f"✅ 登录页面加载成功: {response.status_code == 200}")
                print(f"✅ 包含 CSRF 令牌: {has_csrf_token}")
                
                return True
            else:
                print(f"❌ 登录页面加载失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 登录表单测试失败: {e}")
        return False

def test_csrf_error_handler():
    """测试 CSRF 错误处理"""
    print("\n测试 CSRF 错误处理...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # 尝试不带 CSRF 令牌的 POST 请求
            response = client.post('/admin/login', data={
                'email': '<EMAIL>',
                'password': 'test123'
            }, follow_redirects=False)
            
            # 应该返回重定向或错误
            print(f"✅ 无 CSRF 令牌的请求被拒绝: {response.status_code in [302, 400, 403]}")
            
            return True
            
    except Exception as e:
        print(f"❌ CSRF 错误处理测试失败: {e}")
        return False

def test_template_csrf_function():
    """测试模板中的 CSRF 函数"""
    print("\n测试模板 CSRF 函数...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # 测试模板上下文中的 csrf_token 函数
            from flask import render_template_string
            
            template = "{{ csrf_token() }}"
            result = render_template_string(template)
            
            print(f"✅ 模板 CSRF 函数可用: {len(result) > 0}")
            print(f"✅ CSRF 令牌格式正确: {len(result) > 20}")
            
            return True
            
    except Exception as e:
        print(f"❌ 模板 CSRF 函数测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("🔒 CSRF 修复测试")
    print("=" * 60)
    
    tests = [
        test_csrf_configuration,
        test_login_form_csrf,
        test_csrf_error_handler,
        test_template_csrf_function
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有 CSRF 测试通过！登录功能应该正常工作")
        print("\n📋 部署后验证步骤:")
        print("1. 访问 https://your-app.vercel.app/admin/login")
        print("2. 尝试登录 (邮箱: <EMAIL>, 密码: admin123)")
        print("3. 确认没有 CSRF 错误")
        return 0
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return 1

if __name__ == '__main__':
    sys.exit(main())
