#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心功能测试运行器
只运行最重要的、稳定的测试，确保核心功能正常
"""

import os
import sys
import subprocess
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

def run_single_test(test_path, description):
    """运行单个测试"""
    print(f"\n{'='*50}")
    print(f"🧪 {description}")
    print(f"{'='*50}")
    
    if not os.path.exists(test_path):
        print(f"❌ 测试文件不存在: {test_path}")
        return False, 0
    
    try:
        start_time = time.time()
        
        result = subprocess.run([
            sys.executable, test_path
        ], 
        cwd=project_root,
        capture_output=True, 
        text=True,
        timeout=60  # 1分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 只显示关键输出
        if result.returncode == 0:
            # 提取成功率信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if '成功率:' in line or '通过:' in line or '失败:' in line:
                    print(f"  {line.strip()}")
            
            print(f"✅ {description} - 通过 ({duration:.2f}s)")
            return True, duration
        else:
            # 显示错误摘要
            print("❌ 测试失败，主要错误:")
            error_lines = result.stderr.split('\n')
            for line in error_lines[-5:]:  # 只显示最后5行错误
                if line.strip():
                    print(f"  {line.strip()}")
            
            print(f"❌ {description} - 失败 ({duration:.2f}s)")
            return False, duration
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False, 60
    except Exception as e:
        print(f"💥 {description} - 运行出错: {e}")
        return False, 0

def main():
    """主函数"""
    print("🧪 Web3项目深度分析报告 - 核心功能测试")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 核心测试列表（只包含最稳定、最重要的测试）
    essential_tests = [
        ("test/unit/test_models.py", "数据库模型单元测试 (最重要)"),
    ]
    
    # 可选测试列表（如果有时间可以运行）
    optional_tests = [
        ("test/e2e/test_homepage_template.py", "首页模板测试"),
        ("test/e2e/test_seo_features.py", "SEO功能测试"),
    ]
    
    print("🎯 运行核心功能测试...")
    
    # 运行核心测试
    core_passed = 0
    core_total = len(essential_tests)
    core_duration = 0
    
    for test_path, description in essential_tests:
        success, duration = run_single_test(test_path, description)
        core_duration += duration
        if success:
            core_passed += 1
    
    # 核心测试结果
    print(f"\n{'='*60}")
    print("📊 核心测试结果")
    print(f"{'='*60}")
    print(f"核心测试: {core_passed}/{core_total} 通过")
    print(f"核心成功率: {(core_passed/core_total)*100:.1f}%")
    
    if core_passed == core_total:
        print("✅ 核心功能测试全部通过！系统核心功能正常")
        
        # 如果核心测试通过，运行可选测试
        print(f"\n🔍 运行可选测试...")
        
        optional_passed = 0
        optional_total = len(optional_tests)
        optional_duration = 0
        
        for test_path, description in optional_tests:
            success, duration = run_single_test(test_path, description)
            optional_duration += duration
            if success:
                optional_passed += 1
        
        print(f"\n{'='*60}")
        print("📊 完整测试结果")
        print(f"{'='*60}")
        print(f"核心测试: {core_passed}/{core_total} 通过 (必须100%)")
        print(f"可选测试: {optional_passed}/{optional_total} 通过")
        print(f"总耗时: {(core_duration + optional_duration):.2f}s")
        
        if optional_passed >= optional_total * 0.7:
            print("\n🎉 系统状态优秀！")
            print("✅ 核心功能完全正常")
            print("✅ 大部分扩展功能正常")
            print("✅ 可以安全部署或继续开发")
            return True
        else:
            print("\n⚠️  系统状态良好")
            print("✅ 核心功能完全正常")
            print("⚠️  部分扩展功能需要优化")
            print("✅ 可以部署，建议修复可选功能")
            return True
    else:
        print(f"\n🚨 核心功能测试失败！")
        print(f"❌ 有 {core_total - core_passed} 个核心测试失败")
        print("🛠️  必须修复核心功能问题后才能部署")
        print("\n🔧 建议操作:")
        print("1. 检查数据库连接配置")
        print("2. 验证环境变量设置")
        print("3. 确保所有依赖包已安装")
        return False

if __name__ == '__main__':
    success = main()
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    sys.exit(0 if success else 1)
