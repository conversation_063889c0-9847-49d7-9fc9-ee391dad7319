import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', 587))
        self.username = os.getenv('EMAIL_USERNAME')
        self.password = os.getenv('EMAIL_PASSWORD')
        self.from_email = os.getenv('FROM_EMAIL', self.username)
        
        # 检查配置
        if not all([self.username, self.password]):
            logger.warning("Email service not configured properly")
            self.enabled = False
        else:
            self.enabled = True
    
    def send_email(self, to_emails: List[str], subject: str, 
                   html_content: str, text_content: Optional[str] = None,
                   attachments: Optional[List[str]] = None) -> bool:
        """发送邮件"""
        if not self.enabled:
            logger.warning("Email service is not enabled")
            return False
        
        try:
            # 创建邮件对象
            msg = MIMEMultipart('alternative')
            msg['From'] = self.from_email
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject
            
            # 添加文本内容
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # 添加HTML内容
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)
            
            # 添加附件
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        msg.attach(part)
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {', '.join(to_emails)}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    def send_user_request_notification(self, admin_emails: List[str], 
                                     request_data: Dict[str, Any]) -> bool:
        """发送用户请求通知给管理员"""
        subject = f"新的项目研究请求 - {request_data['project_name']}"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }}
                .content {{ background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }}
                .info-item {{ margin: 10px 0; }}
                .label {{ font-weight: bold; color: #555; }}
                .value {{ color: #333; }}
                .footer {{ margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>新的项目研究请求</h2>
                </div>
                <div class="content">
                    <p>您好，有用户提交了新的项目研究请求，请及时处理：</p>
                    
                    <div class="info-item">
                        <span class="label">项目名称：</span>
                        <span class="value">{request_data['project_name']}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">用户邮箱：</span>
                        <span class="value">{request_data['user_email']}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">官方网站：</span>
                        <span class="value"><a href="{request_data['official_website']}">{request_data['official_website']}</a></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">提交时间：</span>
                        <span class="value">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                    </div>
                    
                    <p style="margin-top: 20px;">
                        <a href="{os.getenv('BASE_URL', 'http://localhost:5000')}/admin/requests" 
                           style="background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                            查看请求详情
                        </a>
                    </p>
                </div>
                <div class="footer">
                    <p>此邮件由项目研究报告平台自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        新的项目研究请求
        
        项目名称：{request_data['project_name']}
        用户邮箱：{request_data['user_email']}
        官方网站：{request_data['official_website']}
        提交时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        请登录管理后台查看详情：{os.getenv('BASE_URL', 'http://localhost:5000')}/admin/requests
        """
        
        return self.send_email(admin_emails, subject, html_content, text_content)
    
    def send_request_status_notification(self, user_email: str, 
                                       request_data: Dict[str, Any]) -> bool:
        """发送请求状态更新通知给用户"""
        status_map = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'rejected': '已拒绝'
        }
        
        status_text = status_map.get(request_data['status'], request_data['status'])
        subject = f"项目研究请求状态更新 - {request_data['project_name']}"
        
        if request_data['status'] == 'completed':
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }}
                    .success {{ background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }}
                    .footer {{ margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>研究报告已完成！</h2>
                    </div>
                    <div class="content">
                        <div class="success">
                            <strong>好消息！</strong> 您申请的项目研究报告已经完成。
                        </div>
                        
                        <p>您好，</p>
                        <p>您申请研究的项目 <strong>{request_data['project_name']}</strong> 的报告已经完成，现在可以在线查看了。</p>
                        
                        <p style="margin-top: 20px;">
                            <a href="{os.getenv('BASE_URL', 'http://localhost:5000')}" 
                               style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                                查看研究报告
                            </a>
                        </p>
                        
                        <p>感谢您使用我们的服务！</p>
                    </div>
                    <div class="footer">
                        <p>此邮件由项目研究报告平台自动发送，请勿回复。</p>
                    </div>
                </div>
            </body>
            </html>
            """
        else:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }}
                    .status {{ padding: 15px; border-radius: 5px; margin: 15px 0; }}
                    .status.processing {{ background: #cff4fc; border: 1px solid #b6effb; color: #055160; }}
                    .status.rejected {{ background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }}
                    .footer {{ margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>请求状态更新</h2>
                    </div>
                    <div class="content">
                        <p>您好，</p>
                        <p>您申请研究的项目 <strong>{request_data['project_name']}</strong> 的状态已更新：</p>
                        
                        <div class="status {request_data['status']}">
                            <strong>当前状态：</strong> {status_text}
                        </div>
                        
                        {f'<p><strong>备注：</strong> {request_data.get("admin_notes", "")}</p>' if request_data.get("admin_notes") else ''}
                        
                        <p>我们会继续跟进您的请求，有任何更新会及时通知您。</p>
                    </div>
                    <div class="footer">
                        <p>此邮件由项目研究报告平台自动发送，请勿回复。</p>
                    </div>
                </div>
            </body>
            </html>
            """
        
        return self.send_email([user_email], subject, html_content)
    
    def send_welcome_email(self, user_email: str, user_name: str) -> bool:
        """发送欢迎邮件"""
        subject = "欢迎使用项目研究报告平台"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }}
                .content {{ background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }}
                .feature {{ margin: 15px 0; padding: 10px; background: white; border-radius: 5px; }}
                .footer {{ margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>欢迎使用项目研究报告平台！</h2>
                </div>
                <div class="content">
                    <p>亲爱的 {user_name}，</p>
                    <p>欢迎使用我们的项目研究报告平台！在这里您可以：</p>
                    
                    <div class="feature">
                        <strong>📊 浏览研究报告</strong><br>
                        查看各种项目的详细研究报告和分析
                    </div>
                    
                    <div class="feature">
                        <strong>🔍 搜索功能</strong><br>
                        快速找到您感兴趣的项目报告
                    </div>
                    
                    <div class="feature">
                        <strong>📝 申请新项目</strong><br>
                        如果找不到想要的项目，可以申请我们研究
                    </div>
                    
                    <p style="margin-top: 20px;">
                        <a href="{os.getenv('BASE_URL', 'http://localhost:5000')}" 
                           style="background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                            开始探索
                        </a>
                    </p>
                </div>
                <div class="footer">
                    <p>此邮件由项目研究报告平台自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return self.send_email([user_email], subject, html_content)

# 全局邮件服务实例
email_service = EmailService()
