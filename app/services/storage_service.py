import os
import uuid
import logging
from typing import Op<PERSON>, Tuple
from werkzeug.utils import secure_filename
from flask import current_app
from app.services.database import db_service
from app.utils.validators import sanitize_filename, validate_file_extension, validate_file_size

logger = logging.getLogger(__name__)

class StorageService:
    """文件存储服务 - 支持本地存储和Supabase Storage"""
    
    def __init__(self):
        self.use_supabase_storage = os.getenv('VERCEL') or os.getenv('USE_SUPABASE_STORAGE', 'false').lower() == 'true'
        
    def _generate_unique_filename(self, original_filename: str) -> str:
        """生成唯一的文件名"""
        if '.' in original_filename:
            name, extension = original_filename.rsplit('.', 1)
            extension = extension.lower()
        else:
            name = original_filename
            extension = ''
        
        # 清理原始文件名
        clean_name = sanitize_filename(name)
        
        # 生成唯一标识符
        unique_id = str(uuid.uuid4())[:8]
        
        # 组合新文件名
        if extension:
            new_filename = f"{clean_name}_{unique_id}.{extension}"
        else:
            new_filename = f"{clean_name}_{unique_id}"
        
        return secure_filename(new_filename)
    
    def save_file(self, file, subfolder: str) -> str:
        """保存文件并返回文件路径/URL"""
        try:
            if not file or not file.filename:
                raise ValueError("No file provided")
            
            # 验证文件大小
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头
            
            max_size = current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
            if not validate_file_size(file_size, max_size):
                raise ValueError(f"File size exceeds maximum allowed size of {max_size} bytes")
            
            # 生成唯一文件名
            filename = self._generate_unique_filename(file.filename)
            
            if self.use_supabase_storage:
                return self._save_to_supabase(file, subfolder, filename)
            else:
                return self._save_to_local(file, subfolder, filename)
                
        except Exception as e:
            logger.error(f"Error saving file: {e}")
            raise
    
    def _save_to_supabase(self, file, subfolder: str, filename: str) -> str:
        """保存文件到Supabase Storage"""
        try:
            # 读取文件内容
            file_content = file.read()
            file.seek(0)  # 重置文件指针
            
            # 构建存储路径
            storage_path = f"{subfolder}/{filename}"
            
            # 上传到Supabase Storage
            bucket_name = 'reports' if subfolder == 'reports' else 'analysis'
            
            if db_service.use_mock:
                # Mock模式下返回模拟路径
                logger.info(f"Mock mode: File would be saved to Supabase Storage: {storage_path}")
                return storage_path
            
            # 实际上传到Supabase（使用service key客户端绕过RLS策略）
            service_client = db_service.get_client(use_service_key=True)
            response = service_client.storage.from_(bucket_name).upload(
                path=filename,
                file=file_content,
                file_options={"content-type": file.content_type or "application/octet-stream"}
            )
            
            if response.status_code == 200:
                logger.info(f"File uploaded to Supabase Storage: {storage_path}")
                return storage_path
            else:
                raise Exception(f"Supabase upload failed: {response}")
                
        except Exception as e:
            logger.error(f"Error uploading to Supabase Storage: {e}")
            raise
    
    def _save_to_local(self, file, subfolder: str, filename: str) -> str:
        """保存文件到本地存储"""
        try:
            # 创建保存目录
            upload_folder = current_app.config['UPLOAD_FOLDER']
            save_dir = os.path.join(upload_folder, subfolder)
            os.makedirs(save_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(save_dir, filename)
            file.save(file_path)
            
            # 返回相对路径
            relative_path = os.path.join(subfolder, filename)
            
            logger.info(f"File saved locally: {relative_path}")
            return relative_path
            
        except Exception as e:
            logger.error(f"Error saving file locally: {e}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            if self.use_supabase_storage:
                return self._delete_from_supabase(file_path)
            else:
                return self._delete_from_local(file_path)
                
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False
    
    def _delete_from_supabase(self, file_path: str) -> bool:
        """从Supabase Storage删除文件"""
        try:
            if db_service.use_mock:
                logger.info(f"Mock mode: File would be deleted from Supabase Storage: {file_path}")
                return True
            
            # 确定bucket
            bucket_name = 'reports' if file_path.startswith('reports/') else 'analysis'
            filename = file_path.split('/', 1)[1] if '/' in file_path else file_path
            
            # 使用service key客户端删除文件
            service_client = db_service.get_client(use_service_key=True)
            response = service_client.storage.from_(bucket_name).remove([filename])
            
            if response:
                logger.info(f"File deleted from Supabase Storage: {file_path}")
                return True
            else:
                logger.warning(f"File not found in Supabase Storage: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting from Supabase Storage: {e}")
            return False
    
    def _delete_from_local(self, file_path: str) -> bool:
        """从本地存储删除文件"""
        try:
            upload_folder = current_app.config['UPLOAD_FOLDER']
            full_path = os.path.join(upload_folder, file_path)
            
            if os.path.exists(full_path):
                os.remove(full_path)
                logger.info(f"File deleted locally: {file_path}")
                return True
            else:
                logger.warning(f"File not found locally: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting local file: {e}")
            return False
    
    def get_file_url(self, file_path: str) -> Optional[str]:
        """获取文件访问URL"""
        try:
            if self.use_supabase_storage:
                return self._get_supabase_url(file_path)
            else:
                return self._get_local_url(file_path)
                
        except Exception as e:
            logger.error(f"Error getting file URL for {file_path}: {e}")
            return None
    
    def _get_supabase_url(self, file_path: str) -> Optional[str]:
        """获取Supabase Storage文件URL"""
        try:
            if db_service.use_mock:
                return f"/static/mock/{file_path}"
            
            # 确定bucket
            bucket_name = 'reports' if file_path.startswith('reports/') else 'analysis'
            filename = file_path.split('/', 1)[1] if '/' in file_path else file_path
            
            # 获取公开URL（使用service key客户端）
            service_client = db_service.get_client(use_service_key=True)
            response = service_client.storage.from_(bucket_name).get_public_url(filename)

            if hasattr(response, 'get') and response.get('publicUrl'):
                return response['publicUrl']
            elif isinstance(response, str):
                return response
            else:
                logger.warning(f"Could not get public URL for: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting Supabase URL: {e}")
            return None
    
    def _get_local_url(self, file_path: str) -> str:
        """获取本地文件URL"""
        return f"/uploads/{file_path}"
    
    def file_exists(self, file_path: str) -> bool:
        """检查文件是否存在"""
        try:
            if self.use_supabase_storage:
                return self._supabase_file_exists(file_path)
            else:
                return self._local_file_exists(file_path)
                
        except Exception as e:
            logger.error(f"Error checking file existence for {file_path}: {e}")
            return False
    
    def _supabase_file_exists(self, file_path: str) -> bool:
        """检查Supabase Storage中文件是否存在"""
        try:
            if db_service.use_mock:
                return True  # Mock模式假设文件存在
            
            bucket_name = 'reports' if file_path.startswith('reports/') else 'analysis'
            filename = file_path.split('/', 1)[1] if '/' in file_path else file_path
            
            # 尝试获取文件信息（使用service key客户端）
            service_client = db_service.get_client(use_service_key=True)
            response = service_client.storage.from_(bucket_name).list(path=filename)
            return len(response) > 0
            
        except Exception as e:
            logger.error(f"Error checking Supabase file existence: {e}")
            return False
    
    def _local_file_exists(self, file_path: str) -> bool:
        """检查本地文件是否存在"""
        try:
            upload_folder = current_app.config['UPLOAD_FOLDER']
            full_path = os.path.join(upload_folder, file_path)
            return os.path.exists(full_path)
            
        except Exception as e:
            logger.error(f"Error checking local file existence: {e}")
            return False


# 创建全局存储服务实例
storage_service = StorageService()
