"""
模拟数据库服务 - 用于演示和测试
在没有真实Supabase配置时使用
"""

import json
import os
import uuid
from datetime import datetime
from typing import Optional, Dict, List, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class MockResult:
    """模拟Supabase查询结果"""
    def __init__(self, data):
        self.data = data

class MockSupabaseService:
    """模拟Supabase数据库服务类"""
    
    def __init__(self):
        self.data_file = 'mock_data.json'
        self.data = self._load_data()
    
    def _load_data(self) -> Dict[str, List[Dict]]:
        """加载模拟数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载模拟数据失败: {e}")
        
        # 返回默认数据结构
        return {
            'admin_users': [
                {
                    'id': str(uuid.uuid4()),
                    'email': '<EMAIL>',
                    'password_hash': 'pbkdf2:sha256:260000$salt$hash',  # 实际应用中应该是真实的哈希
                    'name': '系统管理员',
                    'is_active': True,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }
            ],
            'research_reports': [
                {
                    'id': str(uuid.uuid4()),
                    'project_name': 'React.js',
                    'official_website': 'https://reactjs.org',
                    'creator_name': '研究员A',
                    'report_file_path': 'reports/react_report.md',
                    'analysis_file_path': 'analysis/react_analysis.html',
                    'description': 'React.js是一个用于构建用户界面的JavaScript库',
                    'is_published': True,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'created_by': None
                },
                {
                    'id': str(uuid.uuid4()),
                    'project_name': 'Vue.js',
                    'official_website': 'https://vuejs.org',
                    'creator_name': '研究员B',
                    'report_file_path': 'reports/vue_report.md',
                    'analysis_file_path': 'analysis/vue_analysis.html',
                    'description': 'Vue.js是一个渐进式JavaScript框架',
                    'is_published': True,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'created_by': None
                }
            ],
            'user_requests': [
                {
                    'id': str(uuid.uuid4()),
                    'user_email': '<EMAIL>',
                    'project_name': 'Angular',
                    'official_website': 'https://angular.io',
                    'status': 'pending',
                    'admin_notes': '',
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'processed_by': None,
                    'processed_at': None
                }
            ]
        }
    
    def _save_data(self):
        """保存模拟数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存模拟数据失败: {e}")
    
    def get_client(self, use_service_key: bool = False):
        """获取模拟客户端"""
        return self
    
    def execute_query(self, table: str, operation: str, data: Optional[Dict] = None, 
                     filters: Optional[Dict] = None, use_service_key: bool = False) -> MockResult:
        """执行模拟数据库查询操作"""
        try:
            if table not in self.data:
                self.data[table] = []
            
            if operation == 'select':
                result_data = self.data[table].copy()
                
                # 应用过滤器
                if filters:
                    filtered_data = []
                    for item in result_data:
                        match = True
                        for key, value in filters.items():
                            if isinstance(value, dict):
                                # 复杂查询条件
                                operator = value.get('operator', 'eq')
                                query_value = value.get('value')
                                item_value = item.get(key, '')
                                
                                if operator == 'eq' and item_value != query_value:
                                    match = False
                                elif operator == 'ilike' and query_value.lower() not in str(item_value).lower():
                                    match = False
                                # 可以添加更多操作符
                            else:
                                if item.get(key) != value:
                                    match = False
                        
                        if match:
                            filtered_data.append(item)
                    
                    result_data = filtered_data
                
                return MockResult(result_data)
            
            elif operation == 'insert':
                if data:
                    new_item = data.copy()
                    new_item['id'] = str(uuid.uuid4())
                    new_item['created_at'] = datetime.now().isoformat()
                    new_item['updated_at'] = datetime.now().isoformat()
                    
                    self.data[table].append(new_item)
                    self._save_data()
                    
                    return MockResult([new_item])
                
                return MockResult([])
            
            elif operation == 'update':
                if data and filters:
                    updated_items = []
                    for item in self.data[table]:
                        match = True
                        for key, value in filters.items():
                            if item.get(key) != value:
                                match = False
                        
                        if match:
                            item.update(data)
                            item['updated_at'] = datetime.now().isoformat()
                            updated_items.append(item)
                    
                    if updated_items:
                        self._save_data()
                    
                    return MockResult(updated_items)
                
                return MockResult([])
            
            elif operation == 'delete':
                if filters:
                    remaining_items = []
                    deleted_items = []
                    
                    for item in self.data[table]:
                        match = True
                        for key, value in filters.items():
                            if item.get(key) != value:
                                match = False
                        
                        if match:
                            deleted_items.append(item)
                        else:
                            remaining_items.append(item)
                    
                    self.data[table] = remaining_items
                    
                    if deleted_items:
                        self._save_data()
                    
                    return MockResult(deleted_items)
                
                return MockResult([])
            
            else:
                raise ValueError(f"Unsupported operation: {operation}")
                
        except Exception as e:
            logger.error(f"Mock database operation failed: {e}")
            return MockResult([])
    
    def test_connection(self) -> bool:
        """测试模拟数据库连接"""
        return True

# 创建模拟数据库服务实例
mock_db_service = MockSupabaseService()
