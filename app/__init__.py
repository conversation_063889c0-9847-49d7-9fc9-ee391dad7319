from flask import Flask
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_app():
    # 设置模板和静态文件路径
    template_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'templates'))
    static_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'static'))

    app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key')
    app.config['WTF_CSRF_ENABLED'] = True
    app.config['WTF_CSRF_TIME_LIMIT'] = None  # 在 serverless 环境中禁用 CSRF 时间限制
    app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
    app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))

    # Serverless 环境特殊配置
    if os.getenv('VERCEL'):
        app.config['SESSION_COOKIE_SECURE'] = True
        app.config['SESSION_COOKIE_HTTPONLY'] = True
        app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    
    # Initialize extensions
    csrf = CSRFProtect(app)
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'admin.login'
    login_manager.login_message = '请先登录以访问此页面。'
    login_manager.login_message_category = 'info'

    # Add CSRF token to template context
    @app.context_processor
    def inject_csrf_token():
        from flask_wtf.csrf import generate_csrf
        return dict(csrf_token=generate_csrf)

    # CSRF error handler
    @app.errorhandler(400)
    def handle_csrf_error(e):
        from flask import flash, redirect, url_for, request
        if 'CSRF' in str(e):
            flash('安全验证失败，请重新提交表单。', 'error')
            if request.endpoint and 'admin' in request.endpoint:
                return redirect(url_for('admin.login'))
            return redirect(url_for('public.index'))
        return e
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models.admin_user import AdminUser
        return AdminUser.get_by_id(user_id)

    # 添加自定义模板过滤器
    @app.template_filter('datetime')
    def datetime_filter(value, format='%Y-%m-%d %H:%M'):
        """格式化日期时间"""
        if value is None:
            return '未知'

        # 如果是字符串，尝试解析
        if isinstance(value, str):
            try:
                from datetime import datetime
                # 尝试解析 ISO 格式
                if 'T' in value:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                else:
                    return value  # 如果无法解析，返回原字符串
            except:
                return value

        # 如果有 strftime 方法，使用它
        if hasattr(value, 'strftime'):
            return value.strftime(format)

        return str(value)

    @app.template_filter('timeago')
    def timeago_filter(value):
        """显示相对时间（如：2小时前）"""
        if value is None:
            return '未知时间'

        try:
            from datetime import datetime, timezone

            # 如果是字符串，尝试解析
            if isinstance(value, str):
                if 'T' in value:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                else:
                    return value

            # 确保有时区信息
            if hasattr(value, 'replace') and value.tzinfo is None:
                value = value.replace(tzinfo=timezone.utc)

            now = datetime.now(timezone.utc)
            diff = now - value

            seconds = diff.total_seconds()

            if seconds < 60:
                return '刚刚'
            elif seconds < 3600:
                minutes = int(seconds / 60)
                return f'{minutes}分钟前'
            elif seconds < 86400:
                hours = int(seconds / 3600)
                return f'{hours}小时前'
            elif seconds < 2592000:  # 30 days
                days = int(seconds / 86400)
                return f'{days}天前'
            else:
                return value.strftime('%Y-%m-%d')

        except Exception as e:
            # 如果出错，返回格式化的日期
            if hasattr(value, 'strftime'):
                return value.strftime('%Y-%m-%d')
            return str(value)
    
    # Register blueprints
    from app.views.public import public_bp
    from app.views.admin import admin_bp
    
    app.register_blueprint(public_bp)
    app.register_blueprint(admin_bp, url_prefix='/admin')

    # Add route to serve uploaded files in local development
    if not os.getenv('VERCEL'):
        from flask import send_from_directory

        @app.route('/uploads/<path:filename>')
        def uploaded_file(filename):
            return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    
    # Skip directory creation in serverless environments (like Vercel)
    # Files will be stored in Supabase Storage instead
    if not os.getenv('VERCEL'):
        # Create upload directories only in local development
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'reports'), exist_ok=True)
        os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'analysis'), exist_ok=True)
    
    return app
