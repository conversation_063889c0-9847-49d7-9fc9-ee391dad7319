from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app.services.database import db_service
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class AdminUser(UserMixin):
    """管理员用户模型"""
    
    def __init__(self, user_data: Dict[str, Any]):
        self.id = user_data.get('id')
        self.email = user_data.get('email')
        self.password_hash = user_data.get('password_hash')
        self.name = user_data.get('name')
        self._is_active = user_data.get('is_active', True)
        self.created_at = user_data.get('created_at')
        self.updated_at = user_data.get('updated_at')

    @property
    def is_active(self):
        """Flask-Login 需要的 is_active 属性"""
        return self._is_active
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        # 对于演示模式，简单比较密码
        if self.password_hash == 'pbkdf2:sha256:260000$salt$hash':
            return password == 'admin123'
        return check_password_hash(self.password_hash, password)
    
    @staticmethod
    def get_by_id(user_id: str) -> Optional['AdminUser']:
        """根据ID获取用户"""
        try:
            result = db_service.execute_query(
                'admin_users',
                'select',
                filters={'id': user_id},
                use_service_key=True
            )

            if result.data and len(result.data) > 0:
                return AdminUser(result.data[0])
            return None

        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            return None
    
    @staticmethod
    def get_by_email(email: str) -> Optional['AdminUser']:
        """根据邮箱获取用户"""
        try:
            result = db_service.execute_query(
                'admin_users',
                'select',
                filters={'email': email},
                use_service_key=True
            )

            if result.data and len(result.data) > 0:
                return AdminUser(result.data[0])
            return None

        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            return None
    
    @staticmethod
    def create(user_data: Dict[str, Any]) -> Optional['AdminUser']:
        """创建新用户"""
        try:
            # 哈希密码
            if 'password' in user_data:
                user_data['password_hash'] = generate_password_hash(user_data['password'])
                del user_data['password']
            
            result = db_service.execute_query(
                'admin_users', 
                'insert', 
                data=user_data
            )
            
            if result.data and len(result.data) > 0:
                return AdminUser(result.data[0])
            return None
        
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return None
    
    @staticmethod
    def update(user_id: str, update_data: Dict[str, Any]) -> bool:
        """更新用户信息"""
        try:
            # 如果更新密码，需要哈希
            if 'password' in update_data:
                update_data['password_hash'] = generate_password_hash(update_data['password'])
                del update_data['password']
            
            result = db_service.execute_query(
                'admin_users', 
                'update', 
                data=update_data,
                filters={'id': user_id}
            )
            
            return result.data is not None
        
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            return False
    
    @staticmethod
    def delete(user_id: str) -> bool:
        """删除用户"""
        try:
            result = db_service.execute_query(
                'admin_users', 
                'delete', 
                filters={'id': user_id}
            )
            
            return result.data is not None
        
        except Exception as e:
            logger.error(f"Error deleting user {user_id}: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'email': self.email,
            'name': self.name,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
