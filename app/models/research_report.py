from app.services.database import db_service
from app.services.storage_service import storage_service
from typing import Optional, Dict, Any, List, Tuple
import logging
import os
import markdown
import bleach
import requests
from flask import current_app

logger = logging.getLogger(__name__)

class ResearchReport:
    """研究报告模型"""
    
    @staticmethod
    def get_by_id(report_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取报告"""
        try:
            result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters={'id': report_id}
            )
            
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        
        except Exception as e:
            logger.error(f"Error getting report by ID {report_id}: {e}")
            return None
    
    @staticmethod
    def get_published_reports(page: int = 1, per_page: int = 10, 
                            search_query: str = '') -> Tuple[List[Dict[str, Any]], int]:
        """获取已发布的报告列表（分页）"""
        try:
            offset = (page - 1) * per_page
            
            # 构建查询条件
            filters = {'is_published': True}
            
            # 如果有搜索查询，添加模糊搜索条件
            if search_query:
                filters['project_name'] = {
                    'operator': 'ilike',
                    'value': f'%{search_query}%'
                }
            
            # 获取总数
            count_result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters=filters
            )
            total_count = len(count_result.data) if count_result.data else 0
            
            # 获取分页数据
            # 注意：Supabase的Python客户端可能需要不同的分页方法
            # 这里使用基本的查询，实际使用时可能需要调整
            result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters=filters
            )
            
            if result.data:
                # 手动实现分页（在生产环境中应该在数据库层面实现）
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error getting published reports: {e}")
            return [], 0
    
    @staticmethod
    def get_all_reports(page: int = 1, per_page: int = 10) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有报告列表（管理员用）"""
        try:
            offset = (page - 1) * per_page
            
            # 获取总数
            count_result = db_service.execute_query('research_reports', 'select')
            total_count = len(count_result.data) if count_result.data else 0
            
            # 获取分页数据
            result = db_service.execute_query('research_reports', 'select')
            
            if result.data:
                # 手动实现分页和排序
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error getting all reports: {e}")
            return [], 0
    
    @staticmethod
    def search_reports(query: str, page: int = 1, per_page: int = 10) -> Tuple[List[Dict[str, Any]], int]:
        """搜索报告"""
        try:
            filters = {
                'is_published': True,
                'project_name': {
                    'operator': 'ilike',
                    'value': f'%{query}%'
                }
            }
            
            result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters=filters
            )
            
            if result.data:
                total_count = len(result.data)
                offset = (page - 1) * per_page
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error searching reports: {e}")
            return [], 0
    
    @staticmethod
    def create(report_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建新报告"""
        try:
            result = db_service.execute_query(
                'research_reports',
                'insert',
                data=report_data,
                use_service_key=True  # 使用service key绕过RLS策略
            )

            if result.data and len(result.data) > 0:
                return result.data[0]
            return None

        except Exception as e:
            logger.error(f"Error creating report: {e}")
            return None
    
    @staticmethod
    def update(report_id: str, update_data: Dict[str, Any]) -> bool:
        """更新报告"""
        try:
            result = db_service.execute_query(
                'research_reports',
                'update',
                data=update_data,
                filters={'id': report_id},
                use_service_key=True  # 使用service key绕过RLS策略
            )

            return result.data is not None

        except Exception as e:
            logger.error(f"Error updating report {report_id}: {e}")
            return False
    
    @staticmethod
    def delete(report_id: str) -> bool:
        """删除报告"""
        try:
            result = db_service.execute_query(
                'research_reports',
                'delete',
                filters={'id': report_id},
                use_service_key=True  # 使用service key绕过RLS策略
            )

            return result.data is not None

        except Exception as e:
            logger.error(f"Error deleting report {report_id}: {e}")
            return False

    @staticmethod
    def update_status(report_id: str, is_published: bool) -> bool:
        """更新报告发布状态"""
        try:
            result = db_service.execute_query(
                'research_reports',
                'update',
                filters={'id': report_id},
                data={'is_published': is_published},
                use_service_key=True  # 使用service key绕过RLS策略
            )

            return result.data is not None

        except Exception as e:
            logger.error(f"Error updating report status {report_id}: {e}")
            return False
    
    @staticmethod
    def get_total_count() -> int:
        """获取报告总数"""
        try:
            result = db_service.execute_query('research_reports', 'select')
            return len(result.data) if result.data else 0
        
        except Exception as e:
            logger.error(f"Error getting total count: {e}")
            return 0
    
    @staticmethod
    def get_recent_reports(limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近的报告"""
        try:
            result = db_service.execute_query('research_reports', 'select')
            
            if result.data:
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                return sorted_data[:limit]
            
            return []
        
        except Exception as e:
            logger.error(f"Error getting recent reports: {e}")
            return []
    
    @staticmethod
    def get_report_content(file_path: str) -> str:
        """读取并转换Markdown报告内容"""
        try:
            markdown_content = ResearchReport._read_file_content(file_path)

            if not markdown_content:
                return "报告文件不存在。"

            # 转换Markdown为HTML
            html_content = markdown.markdown(
                markdown_content,
                extensions=['tables', 'fenced_code', 'toc']
            )

            # 清理HTML以防止XSS攻击
            allowed_tags = [
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'p', 'br', 'strong', 'em', 'u', 'strike',
                'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
                'table', 'thead', 'tbody', 'tr', 'th', 'td',
                'a', 'img', 'div', 'span'
            ]

            allowed_attributes = {
                'a': ['href', 'title'],
                'img': ['src', 'alt', 'title', 'width', 'height'],
                'div': ['class'],
                'span': ['class'],
                'table': ['class'],
                'th': ['class'],
                'td': ['class']
            }

            clean_html = bleach.clean(
                html_content,
                tags=allowed_tags,
                attributes=allowed_attributes
            )

            return clean_html

        except Exception as e:
            logger.error(f"Error reading report content from {file_path}: {e}")
            return "读取报告内容时出现错误。"
    
    @staticmethod
    def get_analysis_content(file_path: str) -> str:
        """读取分析页面HTML内容"""
        try:
            html_content = ResearchReport._read_file_content(file_path)

            if not html_content:
                return "<p>分析文件不存在。</p>"

            # 对于分析页面，我们需要更宽松的属性允许列表
            return html_content  # 暂时返回原始内容，实际部署时需要更严格的清理

        except Exception as e:
            logger.error(f"Error reading analysis content from {file_path}: {e}")
            return "<p>读取分析内容时出现错误。</p>"

    @staticmethod
    def _read_file_content(file_path: str) -> Optional[str]:
        """读取文件内容 - 支持本地存储和Supabase Storage"""
        try:
            if storage_service.use_supabase_storage:
                return ResearchReport._read_from_supabase(file_path)
            else:
                return ResearchReport._read_from_local(file_path)
        except Exception as e:
            logger.error(f"Error reading file content from {file_path}: {e}")
            return None

    @staticmethod
    def _read_from_local(file_path: str) -> Optional[str]:
        """从本地存储读取文件内容"""
        try:
            full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)

            if not os.path.exists(full_path):
                return None

            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading local file {file_path}: {e}")
            return None

    @staticmethod
    def _read_from_supabase(file_path: str) -> Optional[str]:
        """从Supabase Storage读取文件内容"""
        try:
            if db_service.use_mock:
                # Mock模式返回示例内容
                if file_path.endswith('.md'):
                    return "# 示例报告\n\n这是一个示例Markdown报告内容。"
                else:
                    return "<h1>示例分析</h1><p>这是一个示例HTML分析内容。</p>"

            # 获取文件的公开URL
            file_url = storage_service.get_file_url(file_path)
            if not file_url:
                logger.error(f"Could not get URL for file: {file_path}")
                return None

            # 从URL下载文件内容
            response = requests.get(file_url, timeout=30)
            response.raise_for_status()

            # 尝试使用UTF-8解码，如果失败则尝试其他编码
            try:
                return response.content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return response.content.decode('gbk')
                except UnicodeDecodeError:
                    return response.content.decode('latin-1')

        except Exception as e:
            logger.error(f"Error reading Supabase file {file_path}: {e}")
            return None
