import os
import uuid
from werkzeug.utils import secure_filename
from flask import current_app
from app.utils.validators import sanitize_filename, validate_file_extension, validate_file_size
from app.services.storage_service import storage_service
import logging

logger = logging.getLogger(__name__)

def allowed_file(filename: str, allowed_extensions: list) -> bool:
    """检查文件是否允许上传"""
    return validate_file_extension(filename, allowed_extensions)

def generate_unique_filename(original_filename: str) -> str:
    """生成唯一的文件名"""
    # 获取文件扩展名
    if '.' in original_filename:
        name, extension = original_filename.rsplit('.', 1)
        extension = extension.lower()
    else:
        name = original_filename
        extension = ''
    
    # 清理原始文件名
    clean_name = sanitize_filename(name)
    
    # 生成唯一标识符
    unique_id = str(uuid.uuid4())[:8]
    
    # 组合新文件名
    if extension:
        new_filename = f"{clean_name}_{unique_id}.{extension}"
    else:
        new_filename = f"{clean_name}_{unique_id}"
    
    return secure_filename(new_filename)

def save_uploaded_file(file, subfolder: str) -> str:
    """保存上传的文件并返回相对路径"""
    try:
        return storage_service.save_file(file, subfolder)
    except Exception as e:
        logger.error(f"Error saving file: {e}")
        raise

def delete_file(file_path: str) -> bool:
    """删除文件"""
    try:
        return storage_service.delete_file(file_path)
    except Exception as e:
        logger.error(f"Error deleting file {file_path}: {e}")
        return False

def get_file_info(file_path: str) -> dict:
    """获取文件信息"""
    try:
        exists = storage_service.file_exists(file_path)
        if not exists:
            return {'exists': False}

        # For Supabase storage, we can't get detailed file stats
        # Return basic info
        return {
            'exists': True,
            'url': storage_service.get_file_url(file_path)
        }

    except Exception as e:
        logger.error(f"Error getting file info for {file_path}: {e}")
        return {'exists': False, 'error': str(e)}

def ensure_upload_directories():
    """确保上传目录存在"""
    try:
        # Skip directory creation in serverless environments
        if os.getenv('VERCEL'):
            logger.info("Skipping directory creation in serverless environment")
            return True

        upload_folder = current_app.config['UPLOAD_FOLDER']

        # 创建主要目录
        directories = [
            upload_folder,
            os.path.join(upload_folder, 'reports'),
            os.path.join(upload_folder, 'analysis'),
            os.path.join(upload_folder, 'temp')
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info("Upload directories ensured")
        return True

    except Exception as e:
        logger.error(f"Error ensuring upload directories: {e}")
        return False

def get_file_url(file_path: str) -> str:
    """获取文件访问URL"""
    return storage_service.get_file_url(file_path) or f"/uploads/{file_path}"
