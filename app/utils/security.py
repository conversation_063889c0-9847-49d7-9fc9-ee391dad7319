import re
import hashlib
import secrets
import logging
from functools import wraps
from flask import request, abort, current_app, session
from werkzeug.security import safe_str_cmp
import bleach
from urllib.parse import urlparse
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

# 允许的HTML标签和属性（用于内容清理）
ALLOWED_TAGS = [
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'p', 'br', 'strong', 'em', 'u', 'strike', 'del',
    'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'a', 'img', 'div', 'span'
]

ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title', 'target'],
    'img': ['src', 'alt', 'title', 'width', 'height'],
    'div': ['class'],
    'span': ['class'],
    'table': ['class'],
    'th': ['class', 'scope'],
    'td': ['class'],
    'blockquote': ['cite'],
    'pre': ['class'],
    'code': ['class']
}

def sanitize_html(content: str, allowed_tags: Optional[List[str]] = None, 
                  allowed_attributes: Optional[Dict[str, List[str]]] = None) -> str:
    """清理HTML内容，防止XSS攻击"""
    if not content:
        return ''
    
    tags = allowed_tags or ALLOWED_TAGS
    attrs = allowed_attributes or ALLOWED_ATTRIBUTES
    
    # 使用bleach清理HTML
    clean_content = bleach.clean(
        content,
        tags=tags,
        attributes=attrs,
        strip=True
    )
    
    # 清理链接，确保只允许安全的协议
    clean_content = bleach.linkify(
        clean_content,
        protocols=['http', 'https', 'mailto'],
        parse_email=True
    )
    
    return clean_content

def validate_input_length(value: str, max_length: int, field_name: str = "字段") -> bool:
    """验证输入长度"""
    if not value:
        return True
    
    if len(value) > max_length:
        logger.warning(f"{field_name} 长度超过限制: {len(value)} > {max_length}")
        return False
    
    return True

def validate_sql_injection(value: str) -> bool:
    """检测SQL注入攻击模式"""
    if not value:
        return True
    
    # SQL注入常见模式
    sql_patterns = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
        r"(--|#|/\*|\*/)",
        r"(\bxp_\w+)",
        r"(\bsp_\w+)",
        r"(\bEXEC\s*\()",
        r"(\bCAST\s*\()",
        r"(\bCONVERT\s*\()"
    ]
    
    value_upper = value.upper()
    
    for pattern in sql_patterns:
        if re.search(pattern, value_upper, re.IGNORECASE):
            logger.warning(f"检测到可能的SQL注入攻击: {value[:100]}")
            return False
    
    return True

def validate_xss_patterns(value: str) -> bool:
    """检测XSS攻击模式"""
    if not value:
        return True
    
    # XSS常见模式
    xss_patterns = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"vbscript:",
        r"onload\s*=",
        r"onerror\s*=",
        r"onclick\s*=",
        r"onmouseover\s*=",
        r"onfocus\s*=",
        r"onblur\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
        r"<form[^>]*>",
        r"<input[^>]*>",
        r"eval\s*\(",
        r"expression\s*\(",
        r"document\.cookie",
        r"document\.write"
    ]
    
    for pattern in xss_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            logger.warning(f"检测到可能的XSS攻击: {value[:100]}")
            return False
    
    return True

def validate_file_path(file_path: str) -> bool:
    """验证文件路径，防止路径遍历攻击"""
    if not file_path:
        return False
    
    # 检查路径遍历模式
    dangerous_patterns = [
        r"\.\./",
        r"\.\.\\",
        r"/\.\./",
        r"\\\.\.\\"
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, file_path):
            logger.warning(f"检测到可能的路径遍历攻击: {file_path}")
            return False
    
    # 检查绝对路径
    if file_path.startswith('/') or file_path.startswith('\\') or ':' in file_path:
        logger.warning(f"检测到绝对路径: {file_path}")
        return False
    
    return True

def rate_limit_check(identifier: str, max_requests: int = 100, 
                    time_window: int = 3600) -> bool:
    """简单的速率限制检查"""
    try:
        # 这里应该使用Redis或其他缓存系统
        # 为了简化，使用session存储（仅适用于开发环境）
        current_time = int(time.time())
        window_start = current_time - time_window
        
        if 'rate_limit' not in session:
            session['rate_limit'] = {}
        
        if identifier not in session['rate_limit']:
            session['rate_limit'][identifier] = []
        
        # 清理过期的请求记录
        session['rate_limit'][identifier] = [
            timestamp for timestamp in session['rate_limit'][identifier]
            if timestamp > window_start
        ]
        
        # 检查是否超过限制
        if len(session['rate_limit'][identifier]) >= max_requests:
            logger.warning(f"速率限制触发: {identifier}")
            return False
        
        # 记录当前请求
        session['rate_limit'][identifier].append(current_time)
        session.modified = True
        
        return True
    
    except Exception as e:
        logger.error(f"速率限制检查失败: {e}")
        return True  # 出错时允许请求

def generate_csrf_token() -> str:
    """生成CSRF令牌"""
    return secrets.token_urlsafe(32)

def validate_csrf_token(token: str) -> bool:
    """验证CSRF令牌"""
    if not token:
        return False
    
    session_token = session.get('csrf_token')
    if not session_token:
        return False
    
    return safe_str_cmp(token, session_token)

def secure_headers(response):
    """添加安全头"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://code.jquery.com https://cdnjs.cloudflare.com; "
        "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "img-src 'self' data: https:; "
        "font-src 'self' https://cdnjs.cloudflare.com; "
        "connect-src 'self';"
    )
    return response

def validate_url_safety(url: str) -> bool:
    """验证URL安全性"""
    if not url:
        return False
    
    try:
        parsed = urlparse(url)
        
        # 检查协议
        if parsed.scheme not in ['http', 'https']:
            return False
        
        # 检查是否为本地地址
        if parsed.hostname in ['localhost', '127.0.0.1', '0.0.0.0']:
            return False
        
        # 检查私有IP地址
        if parsed.hostname:
            import ipaddress
            try:
                ip = ipaddress.ip_address(parsed.hostname)
                if ip.is_private or ip.is_loopback or ip.is_link_local:
                    return False
            except ValueError:
                # 不是IP地址，继续检查
                pass
        
        return True
    
    except Exception:
        return False

def log_security_event(event_type: str, details: Dict[str, Any], 
                      severity: str = 'WARNING'):
    """记录安全事件"""
    security_logger = logging.getLogger('security')
    
    log_data = {
        'event_type': event_type,
        'timestamp': datetime.utcnow().isoformat(),
        'ip_address': request.remote_addr if request else 'unknown',
        'user_agent': request.headers.get('User-Agent') if request else 'unknown',
        'details': details
    }
    
    if severity == 'CRITICAL':
        security_logger.critical(f"Security Event: {log_data}")
    elif severity == 'ERROR':
        security_logger.error(f"Security Event: {log_data}")
    else:
        security_logger.warning(f"Security Event: {log_data}")

def require_rate_limit(max_requests: int = 100, time_window: int = 3600):
    """速率限制装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            identifier = request.remote_addr
            
            if not rate_limit_check(identifier, max_requests, time_window):
                log_security_event(
                    'rate_limit_exceeded',
                    {
                        'endpoint': request.endpoint,
                        'max_requests': max_requests,
                        'time_window': time_window
                    }
                )
                abort(429)  # Too Many Requests
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_input_security(value: str, field_name: str = "输入", 
                          max_length: int = 1000) -> bool:
    """综合输入安全验证"""
    if not value:
        return True
    
    # 长度验证
    if not validate_input_length(value, max_length, field_name):
        return False
    
    # SQL注入检测
    if not validate_sql_injection(value):
        log_security_event(
            'sql_injection_attempt',
            {'field': field_name, 'value': value[:100]}
        )
        return False
    
    # XSS检测
    if not validate_xss_patterns(value):
        log_security_event(
            'xss_attempt',
            {'field': field_name, 'value': value[:100]}
        )
        return False
    
    return True
