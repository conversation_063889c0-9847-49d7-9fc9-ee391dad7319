import os
import logging
import logging.handlers
from datetime import datetime
from flask import request, g
import json

def setup_logging(app):
    """设置应用日志配置"""
    
    # 创建日志目录
    log_dir = os.path.join(app.root_path, '..', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志级别
    log_level = logging.DEBUG if app.config.get('DEBUG') else logging.INFO
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    # 应用主日志
    app_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    app_handler.setLevel(log_level)
    app_handler.setFormatter(formatter)
    
    # 错误日志
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'error.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    
    # 安全日志
    security_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'security.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10
    )
    security_handler.setLevel(logging.WARNING)
    security_handler.setFormatter(detailed_formatter)
    
    # 访问日志
    access_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'access.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    access_handler.setLevel(logging.INFO)
    access_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(message)s'
    ))
    
    # 控制台日志（开发环境）
    if app.config.get('DEBUG'):
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(formatter)
        app.logger.addHandler(console_handler)
    
    # 添加处理器到应用日志
    app.logger.addHandler(app_handler)
    app.logger.addHandler(error_handler)
    app.logger.setLevel(log_level)
    
    # 设置安全日志
    security_logger = logging.getLogger('security')
    security_logger.addHandler(security_handler)
    security_logger.setLevel(logging.WARNING)
    
    # 设置访问日志
    access_logger = logging.getLogger('access')
    access_logger.addHandler(access_handler)
    access_logger.setLevel(logging.INFO)
    
    # 设置数据库日志
    db_logger = logging.getLogger('database')
    db_logger.addHandler(app_handler)
    db_logger.setLevel(log_level)
    
    # 禁用Werkzeug的默认日志（避免重复）
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    
    return app

def log_request():
    """记录请求信息"""
    try:
        access_logger = logging.getLogger('access')
        
        # 收集请求信息
        request_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'method': request.method,
            'url': request.url,
            'endpoint': request.endpoint,
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'referer': request.headers.get('Referer', ''),
            'content_length': request.content_length,
        }
        
        # 如果是POST请求，记录表单字段名（不记录值）
        if request.method == 'POST' and request.form:
            request_data['form_fields'] = list(request.form.keys())
        
        # 记录查询参数
        if request.args:
            request_data['query_params'] = dict(request.args)
        
        access_logger.info(json.dumps(request_data, ensure_ascii=False))
        
    except Exception as e:
        app.logger.error(f"记录访问日志失败: {e}")

def log_response(response):
    """记录响应信息"""
    try:
        access_logger = logging.getLogger('access')
        
        response_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'status_code': response.status_code,
            'content_length': response.content_length,
            'processing_time': getattr(g, 'request_start_time', None)
        }
        
        if hasattr(g, 'request_start_time'):
            processing_time = (datetime.utcnow() - g.request_start_time).total_seconds()
            response_data['processing_time'] = f"{processing_time:.3f}s"
        
        access_logger.info(f"Response: {json.dumps(response_data, ensure_ascii=False)}")
        
    except Exception as e:
        app.logger.error(f"记录响应日志失败: {e}")
    
    return response

def log_error(error):
    """记录错误信息"""
    try:
        error_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'url': request.url if request else 'unknown',
            'method': request.method if request else 'unknown',
            'ip_address': request.remote_addr if request else 'unknown',
            'user_agent': request.headers.get('User-Agent', '') if request else 'unknown'
        }
        
        app.logger.error(f"Application Error: {json.dumps(error_data, ensure_ascii=False)}")
        
    except Exception as e:
        app.logger.error(f"记录错误日志失败: {e}")

class RequestIDFilter(logging.Filter):
    """为日志添加请求ID"""
    
    def filter(self, record):
        record.request_id = getattr(g, 'request_id', 'no-request')
        return True

def setup_request_logging(app):
    """设置请求级别的日志"""
    
    @app.before_request
    def before_request():
        g.request_start_time = datetime.utcnow()
        g.request_id = os.urandom(8).hex()
        log_request()
    
    @app.after_request
    def after_request(response):
        return log_response(response)
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        log_error(error)
        return error
    
    # 添加请求ID过滤器
    request_filter = RequestIDFilter()
    for handler in app.logger.handlers:
        handler.addFilter(request_filter)

def get_logger(name):
    """获取指定名称的日志器"""
    return logging.getLogger(name)

# 便捷的日志函数
def log_info(message, logger_name='app'):
    """记录信息日志"""
    logger = logging.getLogger(logger_name)
    logger.info(message)

def log_warning(message, logger_name='app'):
    """记录警告日志"""
    logger = logging.getLogger(logger_name)
    logger.warning(message)

def log_error(message, logger_name='app'):
    """记录错误日志"""
    logger = logging.getLogger(logger_name)
    logger.error(message)

def log_debug(message, logger_name='app'):
    """记录调试日志"""
    logger = logging.getLogger(logger_name)
    logger.debug(message)

def log_security_event(event_type, details, severity='WARNING'):
    """记录安全事件"""
    security_logger = logging.getLogger('security')
    
    log_data = {
        'event_type': event_type,
        'timestamp': datetime.utcnow().isoformat(),
        'ip_address': request.remote_addr if request else 'unknown',
        'user_agent': request.headers.get('User-Agent') if request else 'unknown',
        'details': details
    }
    
    if severity == 'CRITICAL':
        security_logger.critical(json.dumps(log_data, ensure_ascii=False))
    elif severity == 'ERROR':
        security_logger.error(json.dumps(log_data, ensure_ascii=False))
    else:
        security_logger.warning(json.dumps(log_data, ensure_ascii=False))
