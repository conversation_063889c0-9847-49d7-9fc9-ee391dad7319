import re
from urllib.parse import urlparse
from email_validator import validate_email as email_validate, EmailNotValidError

def validate_email(email: str) -> bool:
    """验证邮箱地址格式"""
    try:
        email_validate(email)
        return True
    except EmailNotValidError:
        return False

def validate_url(url: str) -> bool:
    """验证URL格式"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc]) and result.scheme in ['http', 'https']
    except Exception:
        return False

def validate_project_name(name: str) -> bool:
    """验证项目名称"""
    if not name or len(name.strip()) == 0:
        return False
    
    if len(name) > 255:
        return False
    
    # 检查是否包含特殊字符（可根据需要调整）
    pattern = re.compile(r'^[a-zA-Z0-9\u4e00-\u9fa5\s\-_.()]+$')
    return bool(pattern.match(name))

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除危险字符"""
    # 移除路径分隔符和其他危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:255-len(ext)-1] + '.' + ext if ext else name[:255]
    
    return filename.strip()

def validate_file_size(file_size: int, max_size: int = 16 * 1024 * 1024) -> bool:
    """验证文件大小（默认最大16MB）"""
    return 0 < file_size <= max_size

def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
    """验证文件扩展名"""
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in [ext.lower() for ext in allowed_extensions]
