{% extends "admin/base.html" %}

{% block title %}报告管理 - 管理后台{% endblock %}
{% block page_title %}报告管理{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">
        <i class="fas fa-file-alt me-2"></i>研究报告列表
    </h4>
    <a href="{{ url_for('admin.create_report') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>添加新报告
    </a>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ pagination.total or 0 }}</div>
                    <div class="stat-label">报告总数</div>
                </div>
                <div class="text-primary">
                    <i class="fas fa-file-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 报告列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>报告列表
        </h5>
    </div>
    <div class="card-body">
        {% if reports %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>项目名称</th>
                            <th>创建者</th>
                            <th>官方网站</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for report in reports %}
                        <tr>
                            <td>
                                <div class="fw-bold">{{ report.project_name }}</div>
                                {% if report.description %}
                                <small class="text-muted">{{ report.description[:100] }}{% if report.description|length > 100 %}...{% endif %}</small>
                                {% endif %}
                            </td>
                            <td>{{ report.creator_name }}</td>
                            <td>
                                <a href="{{ report.official_website }}" target="_blank" class="text-decoration-none">
                                    <i class="fas fa-external-link-alt me-1"></i>访问
                                </a>
                            </td>
                            <td>
                                {% if report.is_published %}
                                    <span class="badge bg-success">已发布</span>
                                {% else %}
                                    <span class="badge bg-warning">草稿</span>
                                {% endif %}
                            </td>
                            <td>
                                {{ report.created_at | datetime }}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('public.view_report', report_id=report.id) }}"
                                       class="btn btn-outline-primary" target="_blank" title="预览">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-warning"
                                            data-report-id="{{ report.id }}"
                                            onclick="editReport(this)" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            data-report-id="{{ report.id }}"
                                            data-project-name="{{ report.project_name|e }}"
                                            onclick="deleteReport(this)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% if not report.is_published %}
                                    <button type="button" class="btn btn-outline-success"
                                            data-report-id="{{ report.id }}"
                                            onclick="publishReport(this)" title="发布">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    {% else %}
                                    <button type="button" class="btn btn-outline-secondary"
                                            data-report-id="{{ report.id }}"
                                            onclick="unpublishReport(this)" title="取消发布">
                                        <i class="fas fa-eye-slash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.total_pages > 1 %}
            <nav aria-label="报告分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.reports', page=pagination.prev_num) }}">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.page %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.reports', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.reports', page=pagination.next_num) }}">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无报告</h5>
                <p class="text-muted">点击上方按钮添加第一个研究报告</p>
                <a href="{{ url_for('admin.create_report') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加新报告
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function editReport(button) {
    const reportId = button.getAttribute('data-report-id');
    // 跳转到编辑页面（暂时使用alert提示）
    alert('编辑功能正在开发中，将跳转到编辑页面');
    // window.location.href = `/admin/reports/${reportId}/edit`;
}

function deleteReport(button) {
    const reportId = button.getAttribute('data-report-id');
    const projectName = button.getAttribute('data-project-name');

    if (confirm(`确定要删除报告 "${projectName}" 吗？此操作不可恢复。`)) {
        // 显示加载状态
        const originalContent = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // 发送删除请求
        fetch(`/admin/reports/${reportId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，刷新页面
                location.reload();
            } else {
                alert('删除失败: ' + (data.message || '未知错误'));
                button.disabled = false;
                button.innerHTML = originalContent;
            }
        })
        .catch(error => {
            console.error('删除请求失败:', error);
            alert('删除请求失败，请稍后重试');
            button.disabled = false;
            button.innerHTML = originalContent;
        });
    }
}

function publishReport(button) {
    const reportId = button.getAttribute('data-report-id');
    if (confirm('确定要发布这个报告吗？发布后将在公开页面显示。')) {
        updateReportStatus(button, reportId, true, '发布');
    }
}

function unpublishReport(button) {
    const reportId = button.getAttribute('data-report-id');
    if (confirm('确定要取消发布这个报告吗？取消后将不在公开页面显示。')) {
        updateReportStatus(button, reportId, false, '取消发布');
    }
}

function updateReportStatus(button, reportId, isPublished, actionName) {
    // 显示加载状态
    const originalContent = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // 发送状态更新请求
    fetch(`/admin/reports/${reportId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({
            is_published: isPublished
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新成功，刷新页面
            location.reload();
        } else {
            alert(actionName + '失败: ' + (data.message || '未知错误'));
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    })
    .catch(error => {
        console.error(actionName + '请求失败:', error);
        alert(actionName + '请求失败，请稍后重试');
        button.disabled = false;
        button.innerHTML = originalContent;
    });
}

function getCSRFToken() {
    // 从meta标签或cookie中获取CSRF token
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
}
</script>
{% endblock %}
