{% extends "admin/base.html" %}

{% block title %}创建报告 - 管理后台{% endblock %}
{% block page_title %}创建新报告{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">
        <i class="fas fa-plus me-2"></i>创建新报告
    </h4>
    <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回报告列表
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>报告信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="createReportForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="project_name" class="form-label">
                                    <i class="fas fa-project-diagram me-1"></i>项目名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="project_name" name="project_name" 
                                       value="{{ request.form.get('project_name', '') }}" required>
                                <div class="form-text">请输入项目的完整名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="creator_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>创建者名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="creator_name" name="creator_name" 
                                       value="{{ request.form.get('creator_name', '') }}" required>
                                <div class="form-text">报告创建者的姓名</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="official_website" class="form-label">
                            <i class="fas fa-globe me-1"></i>官方网站 <span class="text-danger">*</span>
                        </label>
                        <input type="url" class="form-control" id="official_website" name="official_website" 
                               value="{{ request.form.get('official_website', '') }}" required
                               placeholder="https://example.com">
                        <div class="form-text">项目的官方网站URL</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-1"></i>项目描述
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="请简要描述这个项目...">{{ request.form.get('description', '') }}</textarea>
                        <div class="form-text">可选：项目的简要描述</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="report_file" class="form-label">
                                    <i class="fas fa-file-alt me-1"></i>报告文件 <span class="text-danger">*</span>
                                </label>
                                <input type="file" class="form-control" id="report_file" name="report_file" 
                                       accept=".md" required>
                                <div class="form-text">请上传Markdown格式的报告文件 (.md)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="analysis_file" class="form-label">
                                    <i class="fas fa-chart-bar me-1"></i>分析文件 <span class="text-danger">*</span>
                                </label>
                                <input type="file" class="form-control" id="analysis_file" name="analysis_file" 
                                       accept=".html,.htm" required>
                                <div class="form-text">请上传HTML格式的分析文件 (.html)</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>重置表单
                        </button>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="previewForm()">
                                <i class="fas fa-eye me-2"></i>预览
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>创建报告
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>创建指南
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-file-alt me-1"></i>报告文件要求
                    </h6>
                    <ul class="small text-muted mb-0">
                        <li>文件格式：Markdown (.md)</li>
                        <li>建议大小：不超过5MB</li>
                        <li>内容结构清晰，包含项目介绍、技术分析等</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-chart-bar me-1"></i>分析文件要求
                    </h6>
                    <ul class="small text-muted mb-0">
                        <li>文件格式：HTML (.html)</li>
                        <li>建议大小：不超过10MB</li>
                        <li>包含图表、数据可视化等分析内容</li>
                    </ul>
                </div>

                <div class="alert alert-info small">
                    <i class="fas fa-lightbulb me-1"></i>
                    <strong>提示：</strong>创建后的报告默认为草稿状态，需要在报告列表中手动发布。
                </div>
            </div>
        </div>

        <!-- 最近创建的报告 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>最近创建
                </h6>
            </div>
            <div class="card-body">
                <div class="small text-muted">
                    <p class="mb-1">暂无最近创建的报告</p>
                    <p class="mb-0">创建第一个报告开始吧！</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetForm() {
    if (confirm('确定要重置表单吗？所有已填写的内容将被清空。')) {
        document.getElementById('createReportForm').reset();
    }
}

function previewForm() {
    const projectName = document.getElementById('project_name').value;
    const creatorName = document.getElementById('creator_name').value;
    const website = document.getElementById('official_website').value;
    const description = document.getElementById('description').value;
    
    if (!projectName || !creatorName || !website) {
        alert('请先填写必填字段再预览');
        return;
    }
    
    const previewContent = `
项目名称: ${projectName}
创建者: ${creatorName}
官方网站: ${website}
描述: ${description || '无'}
    `;
    
    alert('预览信息:\n' + previewContent);
}

// 文件上传验证
document.getElementById('report_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (!file.name.toLowerCase().endsWith('.md')) {
            alert('报告文件必须是Markdown格式 (.md)');
            e.target.value = '';
        } else if (file.size > 5 * 1024 * 1024) {
            alert('报告文件大小不能超过5MB');
            e.target.value = '';
        }
    }
});

document.getElementById('analysis_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const validExtensions = ['.html', '.htm'];
        const isValid = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
        
        if (!isValid) {
            alert('分析文件必须是HTML格式 (.html 或 .htm)');
            e.target.value = '';
        } else if (file.size > 10 * 1024 * 1024) {
            alert('分析文件大小不能超过10MB');
            e.target.value = '';
        }
    }
});

// 表单提交验证
document.getElementById('createReportForm').addEventListener('submit', function(e) {
    const reportFile = document.getElementById('report_file').files[0];
    const analysisFile = document.getElementById('analysis_file').files[0];
    
    if (!reportFile || !analysisFile) {
        e.preventDefault();
        alert('请上传报告文件和分析文件');
        return;
    }
    
    // 显示提交中状态
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>创建中...';
});
</script>
{% endblock %}
