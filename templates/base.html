<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>{% block title %}Web3项目深度分析报告 - 专业区块链项目研究平台{% endblock %}</title>
    <meta name="description" content="{% block description %}专业的Web3项目深度分析报告平台，提供区块链项目研究、DeFi项目评估、加密货币项目分析等服务。获取最新的Web3项目投资建议和技术分析。{% endblock %}">
    <meta name="keywords" content="{% block keywords %}Web3项目分析,区块链项目报告,DeFi项目评估,加密货币项目研究,Web3投资分析,区块链技术分析,数字货币项目,去中心化金融{% endblock %}">
    <meta name="author" content="Web3项目深度分析报告平台">
    <meta name="robots" content="{% block robots %}index, follow{% endblock %}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title() }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ self.description() }}{% endblock %}">
    <meta property="og:type" content="{% block og_type %}website{% endblock %}">
    <meta property="og:url" content="{% block og_url %}{{ request.url }}{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{{ url_for('static', filename='images/og-default.jpg', _external=True) }}{% endblock %}">
    <meta property="og:site_name" content="Web3项目深度分析报告">
    <meta property="og:locale" content="zh_CN">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}{{ self.og_title() }}{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}{{ self.og_description() }}{% endblock %}">
    <meta name="twitter:image" content="{% block twitter_image %}{{ self.og_image() }}{% endblock %}">

    <!-- Canonical URL -->
    <link rel="canonical" href="{% block canonical %}{{ request.url }}{% endblock %}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- Structured Data -->
    {% block structured_data %}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Web3项目深度分析报告",
        "description": "专业的Web3项目深度分析报告平台，提供区块链项目研究、DeFi项目评估、加密货币项目分析等服务",
        "url": "{{ request.url_root }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ url_for('public.index', _external=True) }}?search={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Web3项目深度分析报告平台"
        }
    }
    </script>
    {% endblock %}

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('public.index') }}">
                <i class="fas fa-chart-line me-2"></i>Web3项目深度分析报告
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('public.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <!-- 移除管理员登录入口，管理员通过特定URL访问 -->
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb Navigation -->
    {% block breadcrumb %}
    <nav aria-label="breadcrumb" class="bg-light py-2">
        <div class="container">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('public.index') }}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>首页
                    </a>
                </li>
                {% block breadcrumb_items %}{% endblock %}
            </ol>
        </div>
    </nav>
    {% endblock %}

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container my-4" role="main">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer-custom py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-line me-2"></i>Web3项目深度分析报告
                    </h5>
                    <p class="text-light-emphasis">
                        专业的区块链项目研究平台，提供权威的Web3项目分析、DeFi项目评估和加密货币投资建议。
                    </p>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">项目分类</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{{ url_for('public.category_defi') }}" class="text-light text-decoration-none">
                                DeFi项目
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('public.category_nft') }}" class="text-light text-decoration-none">
                                NFT项目
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('public.category_layer2') }}" class="text-light text-decoration-none">
                                Layer2项目
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('public.category_gamefi') }}" class="text-light text-decoration-none">
                                GameFi项目
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">快速导航</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{{ url_for('public.index') }}" class="text-light text-decoration-none">
                                最新报告
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('public.web3_projects') }}" class="text-light text-decoration-none">
                                Web3项目
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('public.blockchain_analysis') }}" class="text-light text-decoration-none">
                                区块链分析
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="{{ url_for('public.reports_list') }}" class="text-light text-decoration-none">
                                所有报告
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="mb-3">关于我们</h6>
                    <p class="text-light mb-3">
                        我们致力于为Web3投资者和开发者提供最专业、最及时的区块链项目分析报告。
                    </p>
                    <div class="d-flex flex-wrap gap-2 mb-3">
                        <span class="badge bg-primary">区块链研究</span>
                        <span class="badge bg-success">DeFi分析</span>
                        <span class="badge bg-info">Web3投资</span>
                        <span class="badge bg-warning text-dark">技术审计</span>
                    </div>
                </div>
            </div>

            <hr class="my-4 border-secondary">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-light mb-0">
                        &copy; 2024 Web3项目深度分析报告平台. 保留所有权利.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <!-- SEO链接隐藏但保留在DOM中 -->
                    <div class="seo-links">
                        <a href="{{ url_for('public.robots_txt') }}" class="text-decoration-none small">
                            Robots.txt
                        </a>
                        <a href="{{ url_for('public.sitemap_xml') }}" class="text-decoration-none small">
                            站点地图
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
