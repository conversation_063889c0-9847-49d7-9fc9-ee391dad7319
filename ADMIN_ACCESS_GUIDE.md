# 管理员访问指南

## 🔒 安全访问方式

为了提高系统安全性，管理员登录入口已从用户页面移除。管理员现在需要通过特定的URL直接访问管理后台。

## 🚪 访问入口

### 主要入口
```
/admin/
```
- 直接访问管理员区域
- 未登录用户自动重定向到登录页面
- 已登录用户直接进入仪表板

### 安全入口（推荐）
```
/admin/secure-entry
```
- 更隐蔽的访问路径
- 功能与主要入口相同
- 建议在生产环境中使用

### 直接登录页面
```
/admin/login
```
- 直接访问登录页面
- 适用于已知登录页面地址的情况

## 🔐 登录流程

1. **访问管理员入口**
   ```
   https://your-domain.com/admin/
   ```

2. **自动重定向到登录页面**
   - 如果未登录，系统会自动跳转到登录页面

3. **输入管理员凭据**
   - 邮箱地址
   - 密码

4. **登录成功后进入仪表板**
   - 自动跳转到管理员仪表板
   - 可以管理报告、处理用户请求等

## 🛡️ 安全特性

### 1. 隐蔽性
- ✅ 用户页面不显示管理员入口
- ✅ 导航栏无管理员相关链接
- ✅ 减少被恶意扫描的风险

### 2. 访问控制
- ✅ 需要直接知道URL才能访问
- ✅ 未登录用户无法直接进入管理区域
- ✅ 登录验证机制完整

### 3. 会话管理
- ✅ 安全的会话管理
- ✅ 退出后重定向到登录页面
- ✅ 防止未授权访问

## 📱 使用示例

### 本地开发环境
```bash
# 启动应用
PORT=5001 python run.py

# 访问管理员入口
http://127.0.0.1:5001/admin/

# 或使用安全入口
http://127.0.0.1:5001/admin/secure-entry
```

### 生产环境
```bash
# 访问管理员入口
https://your-domain.com/admin/

# 推荐使用安全入口
https://your-domain.com/admin/secure-entry
```

## 🔧 管理员功能

登录后可以访问以下功能：

### 仪表板 (`/admin/dashboard`)
- 查看系统统计信息
- 最近的报告和请求
- 系统概览

### 报告管理 (`/admin/reports`)
- 查看所有研究报告
- 编辑报告信息
- 发布/取消发布报告
- 删除报告

### 用户请求管理 (`/admin/requests`)
- 查看用户提交的项目请求
- 处理请求（批准/拒绝）
- 创建对应的研究报告
- 更新请求状态

## 🚨 注意事项

### 1. URL保密
- 不要在公开场所分享管理员访问URL
- 建议使用安全入口而非标准入口
- 定期更换访问路径（如需要）

### 2. 账户安全
- 使用强密码
- 定期更换密码
- 不要在不安全的网络环境下登录

### 3. 访问记录
- 系统会记录登录活动
- 异常访问会被记录
- 建议定期检查访问日志

## 🔄 退出登录

### 方式1：使用导航栏
- 点击右上角的"退出"按钮

### 方式2：直接访问
```
/admin/logout
```

### 退出后行为
- 自动重定向到登录页面
- 会话信息被清除
- 需要重新登录才能访问

## 🛠️ 故障排除

### 无法访问管理员入口
1. 检查URL是否正确
2. 确认应用程序正在运行
3. 检查网络连接

### 登录失败
1. 确认邮箱地址正确
2. 检查密码是否正确
3. 确认管理员账户已创建

### 权限问题
1. 确认账户具有管理员权限
2. 检查账户是否被禁用
3. 联系系统管理员

## 📞 技术支持

如果遇到访问问题，请：

1. 检查本指南的故障排除部分
2. 查看应用程序日志
3. 联系技术支持团队

---

**重要提醒**: 请妥善保管管理员访问信息，确保系统安全！ 🔐
