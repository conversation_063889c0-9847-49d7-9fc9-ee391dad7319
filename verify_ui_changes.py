#!/usr/bin/env python3
"""
验证UI颜色更改脚本
检查导航栏和页脚的新样式是否正确应用
"""

import os
import sys

def check_css_changes():
    """检查CSS文件中的更改"""
    css_file = 'static/css/style.css'
    
    if not os.path.exists(css_file):
        print("❌ CSS文件不存在")
        return False
    
    with open(css_file, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    checks = [
        ('.navbar-custom', '导航栏自定义样式'),
        ('rgba(255, 255, 255, 0.95)', '导航栏半透明背景'),
        ('backdrop-filter: blur(10px)', '导航栏模糊效果'),
        ('.footer-custom', '页脚自定义样式'),
        ('rgba(248, 249, 250, 0.95)', '页脚半透明背景'),
        ('.seo-links', 'SEO链接隐藏样式'),
        ('position: absolute', 'SEO链接绝对定位'),
        ('left: -9999px', 'SEO链接屏幕外定位'),
        ('#495057', '深灰色调'),
        ('#6c757d', '中灰色调'),
    ]
    
    print("🎨 检查CSS样式更改...")
    all_passed = True
    
    for check, description in checks:
        if check in css_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    return all_passed


def check_html_changes():
    """检查HTML模板中的更改"""
    html_file = 'templates/base.html'
    
    if not os.path.exists(html_file):
        print("❌ HTML模板文件不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    checks = [
        ('navbar-custom', '导航栏使用自定义类'),
        ('footer-custom', '页脚使用自定义类'),
        ('seo-links', 'SEO链接使用隐藏类'),
        ('<!-- SEO链接隐藏但保留在DOM中 -->', 'SEO链接注释说明'),
    ]
    
    # 检查是否移除了旧的类
    old_classes = [
        ('bg-primary', '旧的导航栏背景类'),
        ('bg-dark', '旧的页脚背景类'),
    ]
    
    print("\n🏗️  检查HTML模板更改...")
    all_passed = True
    
    for check, description in checks:
        if check in html_content:
            print(f"  ✅ {description}: 已应用")
        else:
            print(f"  ❌ {description}: 未找到")
            all_passed = False
    
    print("\n🗑️  检查旧样式是否已移除...")
    for old_class, description in old_classes:
        # 检查是否在导航栏或页脚中使用了旧类
        if f'navbar navbar-expand-lg navbar-dark {old_class}' in html_content:
            print(f"  ❌ {description}: 仍在使用")
            all_passed = False
        elif f'footer class="{old_class}' in html_content:
            print(f"  ❌ {description}: 仍在使用")
            all_passed = False
        else:
            print(f"  ✅ {description}: 已移除")
    
    return all_passed


def main():
    """主函数"""
    print("🔍 验证UI颜色更改")
    print("=" * 50)
    
    css_ok = check_css_changes()
    html_ok = check_html_changes()
    
    print("\n" + "=" * 50)
    print("📊 验证结果")
    print("=" * 50)
    
    if css_ok and html_ok:
        print("✅ 所有更改已正确应用！")
        print("\n🎨 新的设计特点:")
        print("  • 导航栏: 半透明白色背景，柔和灰色文字")
        print("  • 页脚: 半透明浅灰背景，协调的灰色文字")
        print("  • 模糊效果: 增加了backdrop-filter模糊效果")
        print("  • SEO链接: 隐藏但保留在DOM中，不影响SEO")
        print("  • 颜色协调: 使用柔和的灰色调，与白色内容区域协调")
        return True
    else:
        print("❌ 部分更改未正确应用，请检查上述错误")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
