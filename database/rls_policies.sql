-- 启用行级安全策略
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_requests ENABLE ROW LEVEL SECURITY;

-- 管理员用户表策略
-- 只允许认证用户查看自己的信息
CREATE POLICY "Users can view own profile" ON admin_users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" ON admin_users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- 研究报告表策略
-- 允许所有人查看已发布的报告
CREATE POLICY "Anyone can view published reports" ON research_reports
    FOR SELECT USING (is_published = true);

-- 允许认证用户创建报告
CREATE POLICY "Authenticated users can create reports" ON research_reports
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 允许报告创建者更新自己的报告
CREATE POLICY "Users can update own reports" ON research_reports
    FOR UPDATE USING (auth.uid()::text = created_by::text);

-- 允许报告创建者删除自己的报告
CREATE POLICY "Users can delete own reports" ON research_reports
    FOR DELETE USING (auth.uid()::text = created_by::text);

-- 用户请求表策略
-- 允许所有人创建请求
CREATE POLICY "Anyone can create requests" ON user_requests
    FOR INSERT WITH CHECK (true);

-- 允许所有人查看请求（用于统计）
CREATE POLICY "Anyone can view requests" ON user_requests
    FOR SELECT USING (true);

-- 允许认证用户更新请求
CREATE POLICY "Authenticated users can update requests" ON user_requests
    FOR UPDATE USING (auth.role() = 'authenticated');

-- 或者，如果你想要更宽松的策略（用于开发/演示）：
-- 禁用RLS（不推荐用于生产环境）
-- ALTER TABLE admin_users DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE research_reports DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE user_requests DISABLE ROW LEVEL SECURITY;
