-- 创建管理员用户表
CREATE TABLE admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建研究报告表
CREATE TABLE research_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_name VARCHAR(255) NOT NULL,
    official_website VARCHAR(500) NOT NULL,
    creator_name VARCHAR(100) NOT NULL,
    report_file_path VARCHAR(500) NOT NULL, -- Markdown文件路径
    analysis_file_path VARCHAR(500) NOT NULL, -- HTML/JS文件路径
    description TEXT,
    is_published BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES admin_users(id)
);

-- 创建用户请求表
CREATE TABLE user_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_email VARCHAR(255) NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    official_website VARCHAR(500) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, rejected
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_by UUID REFERENCES admin_users(id),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引以提高查询性能
CREATE INDEX idx_research_reports_project_name ON research_reports(project_name);
CREATE INDEX idx_research_reports_created_at ON research_reports(created_at DESC);
CREATE INDEX idx_research_reports_is_published ON research_reports(is_published);
CREATE INDEX idx_user_requests_status ON user_requests(status);
CREATE INDEX idx_user_requests_created_at ON user_requests(created_at DESC);
CREATE INDEX idx_user_requests_user_email ON user_requests(user_email);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间戳触发器
CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_reports_updated_at BEFORE UPDATE ON research_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_requests_updated_at BEFORE UPDATE ON user_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户（密码需要在应用中哈希）
-- 注意：实际部署时应该通过应用程序创建管理员用户
INSERT INTO admin_users (email, password_hash, name) VALUES 
('<EMAIL>', 'placeholder_hash', 'System Administrator');

-- 插入示例研究报告数据
INSERT INTO research_reports (project_name, official_website, creator_name, report_file_path, analysis_file_path, description, created_by) VALUES 
('示例项目1', 'https://example1.com', '研究员A', 'reports/example1.md', 'analysis/example1.html', '这是一个示例项目的研究报告', (SELECT id FROM admin_users WHERE email = '<EMAIL>')),
('示例项目2', 'https://example2.com', '研究员B', 'reports/example2.md', 'analysis/example2.html', '这是另一个示例项目的研究报告', (SELECT id FROM admin_users WHERE email = '<EMAIL>'));
