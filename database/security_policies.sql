-- 启用行级安全 (Row Level Security)
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_requests ENABLE ROW LEVEL SECURITY;

-- 管理员用户表策略
-- 只允许通过service_role访问
CREATE POLICY "Admin users are only accessible via service role" ON admin_users
    FOR ALL USING (auth.role() = 'service_role');

-- 研究报告表策略
-- 公开读取已发布的报告
CREATE POLICY "Published reports are publicly readable" ON research_reports
    FOR SELECT USING (is_published = true);

-- 只允许通过service_role进行写操作
CREATE POLICY "Reports are writable via service role" ON research_reports
    FOR ALL USING (auth.role() = 'service_role');

-- 用户请求表策略
-- 允许匿名用户插入请求
CREATE POLICY "Anyone can insert user requests" ON user_requests
    FOR INSERT WITH CHECK (true);

-- 只允许通过service_role读取和更新
CREATE POLICY "User requests are manageable via service role" ON user_requests
    FOR ALL USING (auth.role() = 'service_role');

-- 创建用于公共访问的视图
CREATE OR REPLACE VIEW public_reports AS
SELECT 
    id,
    project_name,
    official_website,
    creator_name,
    description,
    created_at,
    updated_at
FROM research_reports 
WHERE is_published = true;

-- 为视图设置权限
GRANT SELECT ON public_reports TO anon;
GRANT SELECT ON public_reports TO authenticated;
