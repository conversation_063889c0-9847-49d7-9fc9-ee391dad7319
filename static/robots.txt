# Robots.txt for Web3项目深度分析报告平台
# 优化搜索引擎爬虫访问

User-agent: *
Allow: /
Allow: /static/
Allow: /report/
Allow: /search

# 禁止访问管理员区域
Disallow: /admin/
Disallow: /admin/*

# 禁止访问私有文件和目录
Disallow: /uploads/
Disallow: /*.log
Disallow: /*.tmp
Disallow: /test/
Disallow: /config/

# 允许访问重要的SEO文件
Allow: /robots.txt
Allow: /sitemap.xml
Allow: /favicon.ico

# 针对主要搜索引擎的特殊规则
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Baiduspider
Allow: /
Crawl-delay: 2

User-agent: YandexBot
Allow: /
Crawl-delay: 2

# 站点地图位置
Sitemap: /sitemap.xml

# 针对Web3和区块链内容的优化
# 允许爬取所有公开的项目分析报告
Allow: /report/*/report
Allow: /report/*/analysis

# 爬取频率建议
# 主页和报告列表页面：每日
# 具体报告页面：每周
# 分析页面：每周
