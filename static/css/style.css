/* 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 表格样式 */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* 分页样式 */
.pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    color: #495057;
    font-weight: 600;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
    color: #495057;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-info {
    background-color: #cff4fc;
    color: #055160;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0a3622;
}

.alert-danger {
    background-color: #f8d7da;
    color: #58151c;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

/* 图标样式 */
.fas, .far {
    margin-right: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
        border-radius: 0.375rem !important;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .d-flex.justify-content-between .btn {
        align-self: flex-start;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .pagination {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* 状态标签 */
.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.status-processing {
    background-color: #cff4fc;
    color: #055160;
}

.status-completed {
    background-color: #d1e7dd;
    color: #0a3622;
}

.status-rejected {
    background-color: #f8d7da;
    color: #58151c;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 搜索高亮 */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

/* SEO和性能优化增强 */

/* 性能优化：使用CSS变量 */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition-fast: all 0.15s ease-in-out;
}

/* 基础性能优化 */
html {
    scroll-behavior: smooth;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 图片性能优化 */
img {
    max-width: 100%;
    height: auto;
}

/* Core Web Vitals 优化 */
.hero-section {
    min-height: 400px; /* 预设高度防止CLS */
    will-change: transform;
}

.search-section {
    min-height: 200px; /* 预设高度防止CLS */
}

/* 硬件加速优化 */
.card:hover {
    will-change: transform, box-shadow;
}

.btn:hover {
    will-change: transform, background;
}

/* 移动端性能优化 */
@media (max-width: 576px) {
    .hero-section {
        min-height: 300px;
    }

    .hero-section h1 {
        font-size: 1.75rem !important;
    }

    /* 移动端导航优化 */
    .navbar-collapse {
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 0.5rem;
        margin-top: 0.5rem;
        padding: 1rem;
    }
}

/* 可访问性增强 */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 减少动画对于偏好减少动画的用户 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    .form-control {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
}

/* 打印样式优化 */
@media print {
    .navbar,
    .breadcrumb,
    .btn,
    .pagination,
    .hero-section {
        display: none !important;
    }

    .container {
        max-width: none !important;
        padding: 0 !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3, h4, h5, h6 {
        break-after: avoid;
    }
}

/* 性能监控辅助类 */
.perf-critical {
    contain: layout style paint;
}

.gpu-accelerated {
    will-change: transform;
    transform: translateZ(0);
}
