# Vercel 部署指南

## 问题解决

您遇到的错误是因为 Vercel 的 serverless 环境具有只读文件系统，无法创建本地目录。我已经修复了这个问题。

## 修复内容

1. **更新了 Flask 应用初始化** (`app/__init__.py`)
   - 在 serverless 环境中跳过目录创建
   - 添加了环境变量检测

2. **创建了存储服务** (`app/services/storage_service.py`)
   - 支持本地存储和 Supabase Storage
   - 自动检测运行环境

3. **更新了文件处理** (`app/utils/file_handler.py`)
   - 使用新的存储服务
   - 支持 serverless 环境

4. **更新了报告模型** (`app/models/research_report.py`)
   - 支持从 Supabase Storage 读取文件
   - 添加了 HTTP 下载功能

5. **更新了 Vercel 配置** (`vercel.json`)
   - 添加了必要的环境变量

## 部署步骤

### 1. 确保 Supabase 配置正确

在 Vercel 项目设置中添加以下环境变量：

```
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-anon-public-key
SUPABASE_SERVICE_KEY=your-service-role-key
FLASK_SECRET_KEY=your-production-secret-key
FLASK_ENV=production
DEBUG=False
VERCEL=1
USE_SUPABASE_STORAGE=true
```

### 2. 设置 Supabase Storage

1. 在 Supabase 项目中创建两个 Storage Bucket：
   - `reports` (用于存储 Markdown 文件)
   - `analysis` (用于存储 HTML 文件)

2. 设置 Bucket 为公开访问：
   ```sql
   -- 在 Supabase SQL Editor 中执行
   INSERT INTO storage.buckets (id, name, public) VALUES ('reports', 'reports', true);
   INSERT INTO storage.buckets (id, name, public) VALUES ('analysis', 'analysis', true);
   ```

3. 设置 Storage 策略：
   ```sql
   -- 允许公开读取
   CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (true);
   
   -- 允许认证用户上传
   CREATE POLICY "Authenticated users can upload" ON storage.objects 
   FOR INSERT WITH CHECK (auth.role() = 'authenticated');
   ```

### 3. 部署到 Vercel

1. 推送代码到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### 4. 验证部署

访问以下端点验证部署：

- `https://your-app.vercel.app/health` - 健康检查
- `https://your-app.vercel.app/` - 主页

## 本地开发

本地开发时，应用会自动使用本地文件存储。确保设置正确的环境变量：

```bash
# .env
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-anon-public-key
SUPABASE_SERVICE_KEY=your-service-role-key
FLASK_SECRET_KEY=your-secret-key
FLASK_ENV=development
DEBUG=True
# 不要设置 VERCEL=1 或 USE_SUPABASE_STORAGE=true
```

## 故障排除

### 1. 文件上传失败

检查：
- Supabase Storage Bucket 是否存在
- Storage 策略是否正确设置
- 环境变量是否正确配置

### 2. 文件读取失败

检查：
- 文件是否成功上传到 Supabase Storage
- Bucket 是否设置为公开访问
- 网络连接是否正常

### 3. 部署失败

检查：
- 所有依赖是否在 requirements.txt 中
- 环境变量是否正确设置
- vercel.json 配置是否正确

## 注意事项

1. **文件大小限制**: Vercel 有请求大小限制，大文件上传可能需要额外处理
2. **超时限制**: Vercel serverless 函数有执行时间限制
3. **存储成本**: Supabase Storage 使用量会产生费用
4. **安全性**: 确保 Storage 策略正确设置，避免未授权访问

## 下一步

部署成功后，您可以：

1. 测试文件上传功能
2. 验证报告显示功能
3. 配置自定义域名
4. 设置监控和日志
5. 优化性能和安全性
