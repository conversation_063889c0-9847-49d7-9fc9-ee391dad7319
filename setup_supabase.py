#!/usr/bin/env python3
"""
Supabase初始化脚本
"""

import os
import sys
from werkzeug.security import generate_password_hash
from dotenv import load_dotenv

load_dotenv()
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_supabase():
    """设置Supabase数据库"""
    try:
        from app.services.database import db_service
        
        print("🚀 开始设置Supabase数据库...")
        
        # 测试连接
        if not db_service.test_connection():
            print("❌ 数据库连接失败，请检查配置")
            return False
        
        print("✅ 数据库连接成功")
        
        # 创建管理员用户
        print("\n📝 创建管理员用户...")
        
        # 检查是否已存在管理员
        existing_admin = db_service.execute_query(
            'admin_users',
            'select',
            filters={'email': '<EMAIL>'},
            use_service_key=True
        )
        
        if existing_admin.data:
            print("ℹ️  管理员用户已存在，更新密码...")
            # 更新密码
            password_hash = generate_password_hash('admin123')
            db_service.execute_query(
                'admin_users',
                'update',
                data={'password_hash': password_hash},
                filters={'email': '<EMAIL>'},
                use_service_key=True
            )
        else:
            print("➕ 创建新的管理员用户...")
            # 创建新管理员
            admin_data = {
                'email': '<EMAIL>',
                'password_hash': generate_password_hash('admin123'),
                'name': '系统管理员',
                'is_active': True
            }
            
            result = db_service.execute_query(
                'admin_users',
                'insert',
                data=admin_data,
                use_service_key=True
            )
            
            if not result.data:
                print("❌ 创建管理员用户失败")
                return False
        
        print("✅ 管理员用户设置完成")
        print("   邮箱: <EMAIL>")
        print("   密码: admin123")
        
        # 创建示例报告数据
        print("\n📊 创建示例报告数据...")
        
        # 获取管理员ID
        admin_result = db_service.execute_query(
            'admin_users',
            'select',
            filters={'email': '<EMAIL>'},
            use_service_key=True
        )
        
        if not admin_result.data:
            print("❌ 无法获取管理员ID")
            return False
        
        admin_id = admin_result.data[0]['id']
        
        # 示例报告数据
        sample_reports = [
            {
                'project_name': 'React.js',
                'official_website': 'https://reactjs.org',
                'creator_name': '研究员A',
                'report_file_path': 'reports/react_report.md',
                'analysis_file_path': 'analysis/react_analysis.html',
                'description': 'React.js是一个用于构建用户界面的JavaScript库',
                'is_published': True,
                'created_by': admin_id
            },
            {
                'project_name': 'Vue.js',
                'official_website': 'https://vuejs.org',
                'creator_name': '研究员B',
                'report_file_path': 'reports/vue_report.md',
                'analysis_file_path': 'analysis/vue_analysis.html',
                'description': 'Vue.js是一个渐进式JavaScript框架',
                'is_published': True,
                'created_by': admin_id
            },
            {
                'project_name': 'Angular',
                'official_website': 'https://angular.io',
                'creator_name': '研究员C',
                'report_file_path': 'reports/angular_report.md',
                'analysis_file_path': 'analysis/angular_analysis.html',
                'description': 'Angular是一个基于TypeScript的Web应用框架',
                'is_published': True,
                'created_by': admin_id
            }
        ]
        
        for report_data in sample_reports:
            # 检查是否已存在
            existing = db_service.execute_query(
                'research_reports',
                'select',
                filters={'project_name': report_data['project_name']},
                use_service_key=True
            )
            
            if not existing.data:
                result = db_service.execute_query(
                    'research_reports',
                    'insert',
                    data=report_data,
                    use_service_key=True
                )
                
                if result.data:
                    print(f"✅ 创建示例报告: {report_data['project_name']}")
                else:
                    print(f"❌ 创建示例报告失败: {report_data['project_name']}")
            else:
                print(f"ℹ️  示例报告已存在: {report_data['project_name']}")
        
        # 创建示例用户请求
        print("\n📥 创建示例用户请求...")
        
        sample_requests = [
            {
                'user_email': '<EMAIL>',
                'project_name': 'Svelte',
                'official_website': 'https://svelte.dev',
                'status': 'pending'
            },
            {
                'user_email': '<EMAIL>',
                'project_name': 'Next.js',
                'official_website': 'https://nextjs.org',
                'status': 'processing'
            }
        ]
        
        for request_data in sample_requests:
            # 检查是否已存在
            existing = db_service.execute_query(
                'user_requests',
                'select',
                filters={
                    'user_email': request_data['user_email'],
                    'project_name': request_data['project_name']
                },
                use_service_key=True
            )
            
            if not existing.data:
                result = db_service.execute_query(
                    'user_requests',
                    'insert',
                    data=request_data,
                    use_service_key=True
                )
                
                if result.data:
                    print(f"✅ 创建示例请求: {request_data['project_name']}")
                else:
                    print(f"❌ 创建示例请求失败: {request_data['project_name']}")
            else:
                print(f"ℹ️  示例请求已存在: {request_data['project_name']}")
        
        print("\n🎉 Supabase设置完成！")
        print("\n📋 接下来的步骤：")
        print("1. 上传示例文件到Supabase Storage")
        print("2. 测试应用功能")
        print("3. 部署到生产环境")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置过程中出现错误: {e}")
        return False

def upload_sample_files():
    """上传示例文件到Supabase Storage"""
    print("\n📁 准备上传示例文件...")
    
    # 这里可以添加文件上传逻辑
    # 由于Supabase Python客户端的存储功能，我们建议手动上传或使用Supabase CLI
    
    print("ℹ️  请手动上传以下文件到Supabase Storage：")
    print("   - uploads/reports/react_report.md → reports bucket")
    print("   - uploads/analysis/react_analysis.html → analysis bucket")
    print("   - 其他示例文件...")

if __name__ == '__main__':
    print("=" * 60)
    print("🔧 Supabase 项目研究报告平台初始化")
    print("=" * 60)
    
    # 检查环境变量
    required_vars = ['SUPABASE_URL', 'SUPABASE_KEY', 'SUPABASE_SERVICE_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ 缺少必要的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请在 .env 文件中配置这些变量")
        sys.exit(1)
    
    # 检查是否为演示配置
    if os.getenv('SUPABASE_URL') == 'https://your-project-id.supabase.co':
        print("❌ 请先在 .env 文件中配置真实的Supabase信息")
        print("   从 Supabase 项目设置 → API 页面获取配置信息")
        sys.exit(1)
    
    success = setup_supabase()
    
    if success:
        upload_sample_files()
        print("\n✅ 初始化完成！现在可以启动应用了：")
        print("   python run.py")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1)
